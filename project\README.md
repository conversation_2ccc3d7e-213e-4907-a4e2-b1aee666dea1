# Niteo Studio Website

A professional website for Niteo Studio with a fully functional contact form that sends <NAME_EMAIL>. Perfect for static hosting on platforms like Hostinger.

## ✨ Features

- **Contact Form**: Sends emails <NAME_EMAIL>
- **Free Audit Popup**: Quick audit request form
- **Plan Selection**: Integration with pricing plans and add-ons
- **Form Validation**: Client-side validation with user-friendly error messages
- **Responsive Design**: Works perfectly on all devices
- **Static Hosting Ready**: No backend required - works on any static host

## 🚀 Quick Setup

### 1. Upload to <PERSON>inger
Upload all files to your Hostinger hosting account.

### 2. Configure EmailJS (Required for contact form)
1. Go to [EmailJS.com](https://www.emailjs.com/) and create a free account
2. Follow the detailed setup guide in `SETUP-EMAILJS.md`
3. Update `js/config.js` with your EmailJS credentials

### 3. Test the Contact Form
- Fill out the contact form on your website
- Check <EMAIL> for the email
- Test the "Request Free Audit" popup as well

## 📧 Email Configuration

The contact form uses **EmailJS** - a service that allows sending emails from static websites without a backend server.

### Why EmailJS?
- ✅ Works with static hosting (Hostinger, Netlify, etc.)
- ✅ No server or backend required
- ✅ Free plan includes 200 emails/month
- ✅ Reliable and secure
- ✅ Easy to set up

### Setup Steps:
1. **Read the detailed guide**: `SETUP-EMAILJS.md`
2. **Update config**: Edit `js/config.js` with your EmailJS credentials
3. **Test**: Submit a form and check your email

## 📁 File Structure

```
project/
├── index.html              # Main website file
├── css/                    # Stylesheets
├── js/
│   ├── config.js          # EmailJS configuration (UPDATE THIS)
│   ├── email-service.js   # Email handling service
│   └── ...                # Other JavaScript files
├── assets/                # Images and icons
├── SETUP-EMAILJS.md      # Detailed EmailJS setup guide
└── README.md             # This file
```

## 🔧 Configuration

### Email Settings (`js/config.js`)
```javascript
window.EMAIL_CONFIG = {
  SERVICE_ID: 'your_service_id',           // From EmailJS
  CONTACT_TEMPLATE_ID: 'your_template_id', // Contact form template
  AUDIT_TEMPLATE_ID: 'your_audit_id',     // Audit form template
  PUBLIC_KEY: 'your_public_key',          // EmailJS public key
  TARGET_EMAIL: '<EMAIL>'   // Where emails are sent
};
```

## 📋 Form Fields

### Contact Form:
- Name (required)
- Email (required)
- Phone (optional)
- Project Type (required)
- Current Website URL (conditional - shows based on project type)
- Message (required)
- Selected Plan (optional - from pricing section)
- Selected Add-ons (optional)

### Audit Request Form:
- Name (required)
- Email (required)
- Website URL (required)
- Phone (optional)

## 🛡️ Security & Validation

- Client-side form validation
- Email format validation
- URL validation for website fields
- XSS protection through input sanitization
- Rate limiting through EmailJS

## 🚨 Troubleshooting

### Contact Form Not Working?
1. Check browser console for errors
2. Verify EmailJS configuration in `js/config.js`
3. Ensure EmailJS account is set up correctly
4. Check EmailJS dashboard for error logs

### Emails Not Arriving?
1. Check spam folder
2. Verify email templates in EmailJS dashboard
3. Confirm Gmail service is connected in EmailJS
4. Test templates directly in EmailJS dashboard

### Need Help?
- Read `SETUP-EMAILJS.md` for detailed instructions
- Check EmailJS documentation: https://www.emailjs.com/docs/
- Contact EmailJS support if needed

## 📈 Monitoring

- EmailJS free plan: 200 emails/month
- Monitor usage in EmailJS dashboard
- Upgrade to paid plan if needed for higher volume

---

**Ready to go live?** Just upload to Hostinger and configure EmailJS! 🚀
