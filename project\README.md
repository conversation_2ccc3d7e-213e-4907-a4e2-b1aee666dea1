# Niteo Studio Website with Contact Form

A professional website for Niteo Studio with a fully functional contact form that sends <NAME_EMAIL>.

## Features

- **Contact Form**: Sends emails <NAME_EMAIL>
- **Free Audit Form**: Popup form for website audit requests
- **Plan Selection**: Integration with pricing plans
- **Form Validation**: Both client-side and server-side validation
- **Rate Limiting**: Protection against spam submissions
- **Responsive Design**: Works on all devices

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Configure Email Settings

1. Copy the environment file:
   ```bash
   cp .env.example .env
   ```

2. Set up Gmail App Password:
   - Go to [Google Account Settings](https://myaccount.google.com/apppasswords)
   - Generate an App Password for "Mail"
   - Copy the 16-character password

3. Edit `.env` file:
   ```
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-16-character-app-password-here
   PORT=3000
   NODE_ENV=development
   ```

### 3. Run the Application

For development:
```bash
npm run dev
```

For production:
```bash
npm start
```

The website will be available at `http://localhost:3000`

## Email Configuration

The contact form uses Gmail SMTP to send emails. You need to:

1. **Enable 2-Factor Authentication** on the Gmail account
2. **Generate an App Password** (not your regular Gmail password)
3. **Use the App Password** in the `.env` file

### Gmail App Password Setup:
1. Go to Google Account → Security
2. Enable 2-Step Verification if not already enabled
3. Go to App Passwords
4. Select "Mail" and generate password
5. Use the 16-character password in your `.env` file

## API Endpoints

- `POST /api/contact` - Main contact form submission
- `POST /api/audit` - Free audit request submission
- `GET /api/health` - Health check endpoint

## Form Fields

### Contact Form:
- Name (required)
- Email (required)
- Phone (optional)
- Project Type (required)
- Current Website URL (conditional)
- Message (required)
- Selected Plan (optional)
- Selected Add-ons (optional)

### Audit Form:
- Name (required)
- Email (required)
- Website URL (required)
- Phone (optional)

## Security Features

- **Rate Limiting**: 5 submissions per 15 minutes per IP
- **Input Validation**: Server-side validation with express-validator
- **CORS Protection**: Configured for specific domains
- **Helmet**: Security headers
- **Input Sanitization**: XSS protection

## Deployment

For production deployment:

1. Set `NODE_ENV=production` in your environment
2. Configure proper CORS origins in `server.js`
3. Use a process manager like PM2
4. Set up SSL/HTTPS
5. Configure firewall rules

## Troubleshooting

### Email Not Sending:
1. Check Gmail App Password is correct
2. Verify 2FA is enabled on Gmail account
3. Check server logs for error messages
4. Ensure Gmail account has "Less secure app access" disabled (use App Password instead)

### Form Not Submitting:
1. Check browser console for JavaScript errors
2. Verify server is running on correct port
3. Check network tab for API request status
4. Ensure CORS is properly configured

## Support

For technical support, contact the development team or check the server logs for detailed error messages.
