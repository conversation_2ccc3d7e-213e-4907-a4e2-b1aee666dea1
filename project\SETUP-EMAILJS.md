# EmailJS Setup Guide for Niteo Studio Contact Form

This guide will help you set up EmailJS to make the contact form send <NAME_EMAIL> when hosted on static hosting like Hostinger.

## Step 1: Create EmailJS Account

1. Go to [EmailJS.com](https://www.emailjs.com/)
2. Click "Sign Up" and create a free account
3. Verify your email address

## Step 2: Add Email Service

1. In your EmailJS dashboard, click "Email Services"
2. Click "Add New Service"
3. Choose "Gmail" as your email service
4. Click "Connect Account" and sign <NAME_EMAIL>
5. Give your service a name (e.g., "Niteo Studio Gmail")
6. Note down the **Service ID** (you'll need this later)

## Step 3: Create Email Templates

### Contact Form Template

1. Go to "Email Templates" in your dashboard
2. Click "Create New Template"
3. Name it "Contact Form Submission"
4. Set up the template with this content:

**Subject:** New Contact Form Submission from {{from_name}}

**Body:**
```
New Contact Form Submission

Name: {{from_name}}
Email: {{from_email}}
Phone: {{phone}}
Project Type: {{project_type}}
Current Website: {{current_website}}
Selected Plan: {{selected_plan}}
Selected Add-ons: {{selected_addons}}

Message:
{{message}}

---
Submitted on: {{submission_date}}
```

4. Set the "To Email" to: <EMAIL>
5. Set the "From Email" to: {{from_email}}
6. Set the "From Name" to: {{from_name}}
7. Save the template and note down the **Template ID**

### Audit Request Template

1. Create another template named "Audit Request"
2. Set up with this content:

**Subject:** New Free Audit Request from {{from_name}}

**Body:**
```
New Free Website Audit Request

Name: {{from_name}}
Email: {{from_email}}
Website: {{website}}
Phone: {{phone}}

---
Submitted on: {{submission_date}}
```

3. Set the "To Email" to: <EMAIL>
4. Set the "From Email" to: {{from_email}}
5. Set the "From Name" to: {{from_name}}
6. Save and note down the **Template ID**

## Step 4: Get Your Public Key

1. Go to "Account" in your EmailJS dashboard
2. Find your **Public Key** (also called User ID)
3. Copy this key

## Step 5: Update the Website Code

1. Open `js/email-service.js`
2. Replace the placeholder values with your actual EmailJS credentials:

```javascript
// Replace these with your actual EmailJS values
this.serviceId = 'YOUR_SERVICE_ID'; // From Step 2
this.templateId = 'YOUR_TEMPLATE_ID'; // Contact form template from Step 3
this.auditTemplateId = 'YOUR_AUDIT_TEMPLATE_ID'; // Audit template from Step 3
this.publicKey = 'YOUR_PUBLIC_KEY'; // From Step 4
```

## Step 6: Test the Setup

1. Upload your website to Hostinger
2. Fill out the contact form on your website
3. Check <EMAIL> for the email
4. Test the audit popup form as well

## Step 7: Monitor Usage

- EmailJS free plan includes 200 emails per month
- Monitor your usage in the EmailJS dashboard
- Upgrade to a paid plan if you need more emails

## Troubleshooting

### Emails Not Sending:
1. Check browser console for JavaScript errors
2. Verify all IDs are correct in `email-service.js`
3. Make sure Gmail account is properly connected in EmailJS
4. Check EmailJS dashboard for error logs

### Template Issues:
1. Ensure template variables match exactly (case-sensitive)
2. Test templates in EmailJS dashboard first
3. Check that "To Email" is <NAME_EMAIL>

### CORS Issues:
1. Add your domain to EmailJS allowed origins
2. Go to EmailJS dashboard → Account → Security
3. Add your Hostinger domain (e.g., yoursite.com)

## Security Notes

- EmailJS public key is safe to expose in frontend code
- Never put private keys in frontend JavaScript
- EmailJS handles all email authentication securely
- Rate limiting is built into EmailJS to prevent spam

## Alternative: Formspree Setup

If you prefer Formspree instead of EmailJS:

1. Go to [Formspree.io](https://formspree.io/)
2. Create account and get form endpoint
3. Update form action to point to Formspree endpoint
4. Much simpler but less customizable

## Support

- EmailJS Documentation: https://www.emailjs.com/docs/
- EmailJS Support: <EMAIL>
- For website issues: Check browser console and EmailJS dashboard logs
