# Contact Form Troubleshooting Guide

If you're getting a "something went wrong" error, follow these steps to identify and fix the issue.

## 🔍 Step 1: Check Browser Console

1. **Open your website** in Chrome/Firefox
2. **Press F12** to open Developer Tools
3. **Click the "Console" tab**
4. **Submit the contact form** and look for error messages
5. **Take a screenshot** of any red error messages

## 🧪 Step 2: Use the Test Page

1. **Open `test-form.html`** in your browser
2. **Check the Debug Information** section at the top
3. **Look for any red ❌ or yellow ⚠️ status indicators**
4. **Try submitting the test form**

## 🔧 Step 3: Common Issues & Solutions

### Issue 1: "EmailJS not configured"
**Symptoms:** Yellow warnings in debug info, "Email service not configured" error

**Solution:**
1. Open `js/config.js`
2. Replace placeholder values with your actual EmailJS credentials:
```javascript
window.EMAIL_CONFIG = {
  SERVICE_ID: 'service_xxxxxxx',        // Your actual service ID
  CONTACT_TEMPLATE_ID: 'template_xxxxx', // Your actual template ID
  AUDIT_TEMPLATE_ID: 'template_xxxxx',   // Your actual audit template ID
  PUBLIC_KEY: 'xxxxxxxxxxxxxxxxxx',      // Your actual public key
  TARGET_EMAIL: '<EMAIL>'
};
```

### Issue 2: "EmailJS library not loaded"
**Symptoms:** Red ❌ "EmailJS library not loaded" in debug info

**Solution:**
1. Check your internet connection
2. Make sure the EmailJS CDN is accessible
3. Try refreshing the page

### Issue 3: "Invalid template ID" or "Invalid service ID"
**Symptoms:** Error mentioning template or service not found

**Solution:**
1. Log into your EmailJS dashboard
2. Go to "Email Services" and copy the correct Service ID
3. Go to "Email Templates" and copy the correct Template IDs
4. Update `js/config.js` with the correct IDs

### Issue 4: "Network error"
**Symptoms:** Error mentioning network or fetch issues

**Solution:**
1. Check your internet connection
2. Try submitting the form again
3. Check if your hosting provider blocks external API calls

## 📧 Step 4: Verify EmailJS Setup

### Check Your EmailJS Account:
1. **Login to EmailJS.com**
2. **Email Services**: Make sure Gmail is connected
3. **Email Templates**: Verify templates exist and are published
4. **Account Settings**: Copy your Public Key

### Test Templates in EmailJS Dashboard:
1. Go to your template in EmailJS dashboard
2. Click "Test" button
3. Fill in test data and send
4. Check if email <NAME_EMAIL>

## 🐛 Step 5: Debug Console Messages

Look for these specific console messages:

### ✅ Good Messages:
- "EmailJS library loaded successfully"
- "✅ EmailJS initialized successfully"
- "📧 Attempting to send contact form..."
- "✅ Email sent successfully!"

### ❌ Error Messages:
- "❌ EmailJS library not loaded"
- "⚠️ EmailJS not configured!"
- "❌ Validation errors:"
- "❌ Contact form error:"

## 🔄 Step 6: Quick Fixes

### Fix 1: Clear Browser Cache
1. Press Ctrl+F5 (or Cmd+Shift+R on Mac)
2. Or clear browser cache completely

### Fix 2: Check File Paths
Make sure these files exist and are accessible:
- `js/config.js`
- `js/email-service.js`
- `test-form.html`

### Fix 3: Verify Configuration
Open `js/config.js` and make sure:
- No placeholder values remain (YOUR_SERVICE_ID, etc.)
- All IDs are wrapped in quotes
- No syntax errors (missing commas, quotes)

## 📞 Step 7: Get Help

If you're still having issues, provide this information:

1. **Browser console errors** (screenshot)
2. **Debug status** from test-form.html (screenshot)
3. **EmailJS dashboard** - any error logs
4. **Your config.js** (hide sensitive values)

### Common Error Patterns:

**"TypeError: Cannot read property"**
→ Configuration issue, check config.js

**"Failed to fetch"**
→ Network issue or CORS problem

**"Invalid template ID"**
→ Wrong template ID in config.js

**"Service not found"**
→ Wrong service ID in config.js

## 🎯 Quick Test Checklist

- [ ] EmailJS account created and Gmail connected
- [ ] Email templates created and published
- [ ] `js/config.js` updated with real values
- [ ] Test form shows all green ✅ status
- [ ] Browser console shows no red errors
- [ ] Test email <NAME_EMAIL>

## 📚 Additional Resources

- [EmailJS Documentation](https://www.emailjs.com/docs/)
- [EmailJS Troubleshooting](https://www.emailjs.com/docs/faq/)
- [Gmail Integration Guide](https://www.emailjs.com/docs/examples/gmail/)

---

**Still stuck?** Open `test-form.html`, take screenshots of the debug info and console errors, and we can help you fix it! 🚀
