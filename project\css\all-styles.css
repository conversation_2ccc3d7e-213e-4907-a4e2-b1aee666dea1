/* Consolidated CSS file with all necessary styles */

/* Header styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  transition: all 0.3s ease;
  padding: var(--space-5) 0;
}

.header-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
}

.header.scrolled {
  background-color: var(--color-bg);
  box-shadow: var(--shadow-md);
  padding: var(--space-3) 0;
}

.logo a {
  display: flex;
  align-items: center;
  font-weight: var(--font-weight-bold);
  font-size: 2.6rem;
  color: var(--color-primary);
  text-decoration: none;
}

.logo-icon {
  margin-right: var(--space-3);
  font-size: 2.8rem;
  display: flex;
  align-items: center;
}

.logo-icon img {
  height: 120px;
  width: auto;
  object-fit: contain;
}

.nav-desktop {
  display: none;
}

.nav-desktop ul {
  display: flex;
  gap: var(--space-6);
}

.nav-desktop a {
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
  position: relative;
  transition: color 0.15s ease;
}

.nav-desktop a:hover {
  color: var(--color-primary);
}

.desktop-only {
  display: none;
}

.menu-toggle {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 24px;
  height: 20px;
  cursor: pointer;
}

.menu-toggle .bar {
  width: 100%;
  height: 2px;
  background-color: var(--color-text);
  transition: all 0.3s ease;
}

.menu-toggle.active .bar:nth-child(1) {
  transform: translateY(9px) rotate(45deg);
}

.menu-toggle.active .bar:nth-child(2) {
  opacity: 0;
}

.menu-toggle.active .bar:nth-child(3) {
  transform: translateY(-9px) rotate(-45deg);
}

.nav-mobile {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: var(--color-bg);
  box-shadow: var(--shadow-md);
  padding: var(--space-4);
  display: none;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.nav-mobile.active {
  display: block;
  opacity: 1;
  transform: translateY(0);
}

.nav-mobile ul {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.nav-mobile a {
  display: block;
  padding: var(--space-2) 0;
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
  transition: color 0.15s ease;
}

.nav-mobile a:not(.btn):hover {
  color: var(--color-primary);
}

.nav-mobile .btn {
  margin-top: var(--space-3);
}

@media (min-width: 768px) {
  .nav-desktop {
    display: block;
  }

  .desktop-only {
    display: block;
  }

  .menu-toggle {
    display: none;
  }

  .logo-icon img {
    height: 120px;
  }

  .logo a {
    font-size: 3rem;
  }
}

/* Hero section */
.hero {
  padding-top: calc(6rem + var(--space-9));
  padding-bottom: var(--space-9);
  background: linear-gradient(135deg, #f9fafb 0%, #eff6ff 100%);
  position: relative;
  overflow: hidden;
}

.hero-content {
  text-align: center;
  max-width: 70rem;
  margin: 0 auto var(--space-7) auto;
}

.hero h1 {
  font-size: 4rem;
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
  margin-bottom: var(--space-4);
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.hero p {
  font-size: 2rem;
  color: var(--color-text-light);
  margin-bottom: var(--space-5);
  max-width: 60rem;
  margin-left: auto;
  margin-right: auto;
}

.hero-cta {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  justify-content: center;
  align-items: center;
}

.hero h1 {
  font-size: 4.8rem;
}

@media (min-width: 768px) {
  .hero h1 {
    font-size: 7rem;
  }

  .hero-cta {
    flex-direction: row;
  }
}

@media (min-width: 1024px) {
  .hero h1 {
    font-size: 8.5rem;
  }
}

/* Process section */
.process-timeline {
  max-width: 80rem;
  margin: 0 auto var(--space-7) auto;
  position: relative;
}

.process-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 24px;
  width: 2px;
  background: linear-gradient(to bottom, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.process-step {
  position: relative;
  padding-left: 60px;
  margin-bottom: var(--space-6);
}

.process-step:last-child {
  margin-bottom: 0;
}

.process-number {
  position: absolute;
  left: 0;
  top: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: 2rem;
  z-index: 1;
}

.process-content h3 {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-2);
  color: var(--color-text);
}

.process-content p {
  color: var(--color-text-light);
}

.process-info {
  display: flex;
  justify-content: center;
}

.info-box {
  background-color: var(--color-bg);
  border-radius: var(--border-radius-md);
  padding: var(--space-4);
  box-shadow: var(--shadow-md);
  text-align: center;
}

/* Testimonials section */
.testimonials-slider {
  max-width: 90rem;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
}

.testimonial-track {
  display: flex;
  transition: transform 0.3s ease;
}

.testimonial-slide {
  min-width: 100%;
  padding: 0 var(--space-3);
}

.testimonial-content {
  background-color: var(--color-bg);
  border-radius: var(--border-radius-md);
  padding: var(--space-5);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border);
  transition: transform 0.3s ease;
}

.testimonial-content:hover {
  transform: translateY(-5px);
}

.quote {
  font-size: 1.8rem;
  color: var(--color-text);
  margin-bottom: var(--space-4);
  position: relative;
  line-height: 1.6;
}

.quote::before {
  content: '"';
  font-size: 5rem;
  color: var(--color-primary-light);
  opacity: 0.2;
  position: absolute;
  top: -2rem;
  left: -1rem;
}

.testimonial-info {
  display: flex;
  align-items: center;
  margin-top: var(--space-4);
}

.testimonial-image {
  width: 50px !important;
  height: 50px !important;
  border-radius: 50%;
  object-fit: cover;
  margin-right: var(--space-3);
  max-width: 50px !important;
  max-height: 50px !important;
}

.testimonial-name {
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
}

.testimonial-position {
  font-size: 1.4rem;
  color: var(--color-text-light);
}

.testimonial-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: var(--space-5);
  gap: var(--space-4);
}

.control-prev,
.control-next {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-bg);
  color: var(--color-text);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border);
  transition: all 0.3s ease;
}

.control-prev:hover,
.control-next:hover {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.testimonial-dots {
  display: flex;
  gap: var(--space-2);
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--color-border);
  transition: all 0.3s ease;
}

.dot.active {
  background-color: var(--color-primary);
  transform: scale(1.2);
}

/* Contact section */
.contact-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
}

@media (min-width: 768px) {
  .contact-grid {
    grid-template-columns: 1fr 1fr;
  }
}

.contact-info {
  padding: var(--space-5);
  background-color: var(--color-primary);
  color: white;
  border-radius: var(--border-radius-md);
  height: 100%;
}

.contact-info h3 {
  font-size: 2.4rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-3);
}

.contact-info p {
  margin-bottom: var(--space-5);
  opacity: 0.9;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.contact-method {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
}

.method-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-md);
  flex-shrink: 0;
}

.method-details h4 {
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-1);
}

.method-details a {
  color: white;
  opacity: 0.9;
  transition: opacity 0.3s ease;
}

.method-details a:hover {
  opacity: 1;
  text-decoration: underline;
}

.contact-free-audit {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-md);
  padding: var(--space-4);
}

.contact-free-audit h4 {
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-2);
}

.contact-free-audit p {
  margin-bottom: var(--space-3);
  opacity: 0.9;
  font-size: 1.4rem;
}

.contact-form-container {
  background-color: var(--color-bg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  padding: var(--space-5);
  border: 1px solid var(--color-border);
}

.form-group {
  margin-bottom: var(--space-4);
}

.form-group label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--space-3);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  background-color: var(--color-bg);
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
}

.form-submit {
  margin-top: var(--space-5);
}

.form-response {
  margin-top: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--border-radius-md);
  font-weight: var(--font-weight-medium);
}

.form-response.success {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--color-success);
}

.form-response.error {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--color-error);
}

/* Footer */
.footer {
  background-color: var(--color-text);
  color: var(--color-bg);
  padding: var(--space-7) 0 var(--space-4) 0;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-brand {
  margin-bottom: var(--space-5);
}

.footer-logo {
  display: flex;
  align-items: center;
  font-weight: var(--font-weight-bold);
  font-size: 2.4rem;
  color: white;
  margin-bottom: var(--space-3);
}

.footer-tagline {
  opacity: 0.7;
  max-width: 30rem;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--space-6);
}

.footer-group h4 {
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-3);
  color: white;
}

.footer-group ul {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.footer-group a {
  color: rgba(255, 255, 255, 0.7);
  transition: color 0.3s ease;
}

.footer-group a:hover {
  color: white;
}

.footer-bottom {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-4);
  padding-top: var(--space-4);
}

.footer-social {
  display: flex;
  gap: var(--space-3);
}

.footer-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  transition: all 0.3s ease;
}

.footer-social a:hover {
  background-color: var(--color-primary);
  transform: translateY(-3px);
}

.footer-copyright {
  color: rgba(255, 255, 255, 0.5);
  font-size: 1.4rem;
}

@media (min-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr 2fr;
  }

  .footer-bottom {
    flex-direction: row;
    justify-content: space-between;
  }
}

/* Floating elements */
.floating-cta {
  position: fixed;
  bottom: var(--space-5);
  right: var(--space-5);
  z-index: 100;
}

.btn-floating {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background-color: var(--color-primary);
  color: white;
  border-radius: var(--border-radius-full);
  box-shadow: var(--shadow-xl);
  transition: all 0.3s ease;
}

.btn-floating:hover {
  transform: translateY(-5px);
  background-color: var(--color-primary-dark);
}

.scroll-top {
  position: fixed;
  bottom: var(--space-5);
  left: var(--space-5);
  z-index: 100;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--color-bg);
  color: var(--color-text);
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(20px);
  border: 1px solid var(--color-border);
}

.scroll-top.visible {
  opacity: 1;
  transform: translateY(0);
}

.scroll-top:hover {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

/* Reset */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 62.5%; /* 10px on most browsers */
  scroll-behavior: smooth;
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 1.6rem;
  line-height: 1.5;
  color: #1e293b;
  background-color: #ffffff;
  overflow-x: hidden;
}

img, picture, video, canvas, svg {
  display: block;
  max-width: 100%;
}

input, button, textarea, select {
  font: inherit;
}

p, h1, h2, h3, h4, h5, h6 {
  overflow-wrap: break-word;
}

ul, ol {
  list-style: none;
}

a {
  text-decoration: none;
  color: inherit;
}

button {
  background: none;
  border: none;
  cursor: pointer;
}

/* Variables */
:root {
  /* Colors */
  --color-primary: #4f46e5;
  --color-primary-light: #6366f1;
  --color-primary-dark: #4338ca;
  --color-secondary: #0ea5e9;
  --color-secondary-light: #38bdf8;
  --color-secondary-dark: #0284c7;
  --color-accent: #f97316;
  --color-accent-light: #fb923c;
  --color-accent-dark: #ea580c;

  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;

  --color-bg: #ffffff;
  --color-bg-secondary: #f9fafb;
  --color-bg-tertiary: #f3f4f6;

  --color-text: #1e293b;
  --color-text-light: #64748b;
  --color-text-lighter: #94a3b8;
  --color-border: #e2e8f0;

  /* Spacing */
  --space-1: 0.4rem;
  --space-2: 0.8rem;
  --space-3: 1.2rem;
  --space-4: 1.6rem;
  --space-5: 2.4rem;
  --space-6: 3.2rem;
  --space-7: 4.0rem;
  --space-8: 4.8rem;
  --space-9: 6.4rem;
  --space-10: 8.0rem;

  /* Typography */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;

  /* Borders */
  --border-radius-sm: 0.4rem;
  --border-radius-md: 0.8rem;
  --border-radius-lg: 1.2rem;
  --border-radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Layout */
.container {
  width: 100%;
  max-width: 120rem;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

section {
  padding: var(--space-8) 0;
  position: relative;
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-7);
  width: 100%;
}

/* Center section headers only for specific sections */
.testimonials .section-header,
.pricing .section-header,
.contact .section-header {
  max-width: 80rem;
  margin-left: auto;
  margin-right: auto;
}

.section-header h2 {
  font-size: 3.6rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-2);
  color: var(--color-text);
}

.section-header p {
  font-size: 1.8rem;
  color: var(--color-text-light);
  max-width: 60rem;
  margin: 0 auto;
}

/* Responsive grid layouts */
.services-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--space-5);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--space-5);
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--space-5);
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--space-5);
}

@media (min-width: 768px) {
  .services-grid,
  .features-grid,
  .portfolio-grid,
  .pricing-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .services-grid,
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Utility classes */
.btn {
  display: inline-block;
  font-weight: var(--font-weight-medium);
  text-align: center;
  white-space: nowrap;
  padding: var(--space-3) var(--space-5);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border: 1px solid var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: var(--color-bg);
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.btn-secondary:hover {
  background-color: var(--color-primary-light);
  color: white;
  transform: translateY(-2px);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.btn-outline:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: translateY(-2px);
}

/* Service Cards */
.service-card {
  background-color: var(--color-bg);
  border-radius: var(--border-radius-md);
  padding: var(--space-5);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border);
  transition: all 0.3s ease;
  height: 100%;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.service-icon {
  margin-bottom: var(--space-4);
}

.service-card h3 {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-2);
  color: var(--color-text);
}

.service-card p {
  color: var(--color-text-light);
}

/* Feature Cards */
.feature-card {
  padding: var(--space-5);
  border-radius: var(--border-radius-md);
  background-color: var(--color-bg);
  border: 1px solid var(--color-border);
  transition: all 0.3s ease;
  height: 100%;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.feature-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-md);
  background-color: var(--color-primary-light);
  color: white;
  margin-bottom: var(--space-4);
}

.feature-card h3 {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-2);
  color: var(--color-text);
}

.feature-card p {
  color: var(--color-text-light);
}

/* Portfolio Items */
.portfolio-item {
  background-color: var(--color-bg);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.portfolio-item:hover {
  transform: translateY(-5px);
}

.portfolio-image {
  width: 100%;
  height: 220px;
  overflow: hidden;
}

.portfolio-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.portfolio-item:hover .portfolio-image img {
  transform: scale(1.05);
}

.portfolio-content {
  padding: var(--space-5);
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.portfolio-category {
  font-size: 1.4rem;
  color: var(--color-text-light);
  margin-bottom: var(--space-2);
}

.portfolio-description {
  margin-bottom: var(--space-3);
}

/* Pricing Cards */
.pricing-card {
  background-color: var(--color-bg);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border);
  transition: transform 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.pricing-card:hover {
  transform: translateY(-10px);
}

.pricing-card.featured {
  border-color: var(--color-primary);
  transform: scale(1.03);
  z-index: 1;
}

.pricing-card.featured:hover {
  transform: scale(1.03) translateY(-10px);
}

.pricing-tag {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--color-primary);
  color: white;
  padding: var(--space-2) var(--space-4);
  font-size: 1.4rem;
  font-weight: var(--font-weight-medium);
  border-bottom-left-radius: var(--border-radius-md);
}

.pricing-header {
  padding: var(--space-5);
  text-align: center;
  border-bottom: 1px solid var(--color-border);
}

.pricing-header h3 {
  font-size: 2.4rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-2);
  color: var(--color-text);
}

.pricing-price {
  font-size: 3.2rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-2);
}

.pricing-description {
  color: var(--color-text-light);
  margin-bottom: var(--space-2);
}

.pricing-features {
  padding: var(--space-5);
  flex-grow: 1;
}

.pricing-features ul {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.pricing-features li {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.pricing-features li svg {
  color: var(--color-success);
  flex-shrink: 0;
}

.pricing-card .btn {
  margin: 0 var(--space-5) var(--space-5) var(--space-5);
}

.pricing-note {
  text-align: center;
  margin-top: var(--space-5);
  color: var(--color-text-light);
}

.pricing-note a {
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.pricing-note a:hover {
  text-decoration: underline;
}

/* Animations */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px); /* Reduced movement to minimize layout shifts */
    will-change: opacity; /* Optimize for animation */
  }
  30% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px); /* Reduced movement to minimize layout shifts */
    will-change: opacity; /* Optimize for animation */
  }
  30% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translateX(20px); /* Reduced movement to minimize layout shifts */
    will-change: opacity; /* Optimize for animation */
  }
  30% {
    opacity: 0.3;
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Reserve space for sections before they're animated to prevent layout shifts */
section[data-animate] {
  opacity: 0;
  transform: translateY(0); /* Keep in place to prevent layout shifts */
  pointer-events: none; /* Prevent interaction with hidden sections */
  min-height: 100px; /* Ensure minimum height to prevent layout shifts */
  contain: layout style paint; /* Contain the layout to prevent shifts */
  will-change: opacity; /* Optimize for animation */
}

/* Special handling for services section items - positioned correctly but hidden */
.services .service-card {
  opacity: 0;
  transform: translateY(0); /* Start at correct position */
  pointer-events: none;
  min-height: 100px; /* Ensure minimum height */
  contain: layout style; /* Contain the layout */
}

/* Special handling for process steps - positioned correctly but hidden */
.process .process-step {
  opacity: 0;
  transform: translateX(0); /* Start at correct position */
  pointer-events: none;
  min-height: 50px; /* Ensure minimum height */
  contain: layout style; /* Contain the layout */
}

/* Fallback to ensure elements are visible if JavaScript fails */
.no-js section[data-animate],
.no-js .services .service-card,
.no-js .process .process-step {
  opacity: 1 !important;
  transform: translateY(0) translateX(0) !important;
  pointer-events: auto !important;
  visibility: visible !important;
}

/* Additional fallback for when JavaScript is available but animations fail */
.js .services .service-card,
.js .process .process-step {
  opacity: 1;
  visibility: visible;
  pointer-events: auto;
}

/* Ensure elements become visible after a certain time even if animations fail */
@media screen {
  section[data-animate] {
    animation: ensureVisible 0s 3s forwards;
  }

  @keyframes ensureVisible {
    to {
      opacity: 1;
      transform: translateY(0);
      pointer-events: auto;
      visibility: visible;
    }
  }
}

/* Animation class for entire sections */
section.fade-in {
  animation: fadeIn 1.2s cubic-bezier(0.22, 0.61, 0.36, 1) forwards;
  will-change: opacity, transform, visibility;
  pointer-events: auto; /* Restore interaction when animation starts */
  visibility: visible; /* Make section visible when animation starts */
}

/* Different animations for different sections */
section.services.fade-in,
section.portfolio.fade-in,
section.pricing.fade-in {
  animation: fadeInLeft 1.2s cubic-bezier(0.22, 0.61, 0.36, 1) forwards;
}

section.features.fade-in,
section.process.fade-in,
section.testimonials.fade-in,
section.faq.fade-in,
section.contact.fade-in {
  animation: fadeInRight 1.2s cubic-bezier(0.22, 0.61, 0.36, 1) forwards;
}

/* Hero section animations */
.hero h1.fade-in {
  animation: fadeIn 1.2s cubic-bezier(0.22, 0.61, 0.36, 1) forwards;
}

.hero p.fade-in {
  animation: fadeIn 1s cubic-bezier(0.22, 0.61, 0.36, 1) forwards;
  animation-delay: 0.2s;
}

.hero-cta.fade-in {
  animation: fadeIn 0.8s cubic-bezier(0.22, 0.61, 0.36, 1) forwards;
  animation-delay: 0.4s;
}

/* Mexican wave animation for service cards */
.services .service-card.wave-animate {
  animation: mexicanWave 1.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  pointer-events: auto;
}

/* Row-by-row Mexican wave effect for mobile (1 column) with longer delays */
.services .service-card:nth-child(1).wave-animate { animation-delay: 0.1s; }
.services .service-card:nth-child(2).wave-animate { animation-delay: 0.5s; }
.services .service-card:nth-child(3).wave-animate { animation-delay: 0.9s; }
.services .service-card:nth-child(4).wave-animate { animation-delay: 1.3s; }
.services .service-card:nth-child(5).wave-animate { animation-delay: 1.7s; }
.services .service-card:nth-child(6).wave-animate { animation-delay: 2.1s; }

/* Horizontal Mexican wave animation for process steps */
.process .process-step.wave-animate {
  animation: horizontalWave 1.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
  pointer-events: auto;
}

/* Sequential horizontal wave effect for process steps with longer delays */
.process .process-step:nth-child(1).wave-animate { animation-delay: 0.1s; }
.process .process-step:nth-child(2).wave-animate { animation-delay: 0.6s; }
.process .process-step:nth-child(3).wave-animate { animation-delay: 1.1s; }
.process .process-step:nth-child(4).wave-animate { animation-delay: 1.6s; }

/* Row-by-row Mexican wave effect for tablet (2 columns) */
@media (min-width: 768px) {
  /* First row */
  .services .service-card:nth-child(1).wave-animate { animation-delay: 0.1s; }
  .services .service-card:nth-child(2).wave-animate { animation-delay: 0.5s; }

  /* Second row */
  .services .service-card:nth-child(3).wave-animate { animation-delay: 0.9s; }
  .services .service-card:nth-child(4).wave-animate { animation-delay: 1.3s; }

  /* Third row */
  .services .service-card:nth-child(5).wave-animate { animation-delay: 1.7s; }
  .services .service-card:nth-child(6).wave-animate { animation-delay: 2.1s; }
}

/* Row-by-row Mexican wave effect for desktop (3 columns) */
@media (min-width: 1024px) {
  /* First row */
  .services .service-card:nth-child(1).wave-animate { animation-delay: 0.1s; }
  .services .service-card:nth-child(2).wave-animate { animation-delay: 0.5s; }
  .services .service-card:nth-child(3).wave-animate { animation-delay: 0.9s; }

  /* Second row */
  .services .service-card:nth-child(4).wave-animate { animation-delay: 1.3s; }
  .services .service-card:nth-child(5).wave-animate { animation-delay: 1.7s; }
  .services .service-card:nth-child(6).wave-animate { animation-delay: 2.1s; }
}

/* Mexican Wave animation with bounce effect (vertical) */
@keyframes mexicanWave {
  0% {
    opacity: 0;
    transform: translateY(0);
    visibility: hidden;
  }
  20% {
    opacity: 1;
    transform: translateY(-30px); /* Move up higher than original position */
    visibility: visible;
  }
  60% {
    opacity: 1;
    transform: translateY(-10px); /* Still above original position */
  }
  80% {
    transform: translateY(-5px); /* Slight overshoot */
  }
  100% {
    opacity: 1;
    transform: translateY(0); /* Back to original position */
    visibility: visible;
  }
}

/* Horizontal Mexican Wave animation with bounce effect */
@keyframes horizontalWave {
  0% {
    opacity: 0;
    transform: translateX(-50px);
    visibility: hidden;
  }
  20% {
    opacity: 1;
    transform: translateX(30px); /* Move right further than original position */
    visibility: visible;
  }
  60% {
    opacity: 1;
    transform: translateX(10px); /* Still right of original position */
  }
  80% {
    transform: translateX(5px); /* Slight overshoot */
  }
  100% {
    opacity: 1;
    transform: translateX(0); /* Back to original position */
    visibility: visible;
  }
}

/* Optimize images */
.portfolio-image img, .testimonial-image {
  width: 100%;
  height: auto;
  aspect-ratio: 16/9;
  object-fit: cover;
  will-change: transform;
}

/* Custom Animated Checkboxes for Add-ons */
.addon-checkbox {
  position: absolute !important;
  width: 24px !important;
  height: 24px !important;
  margin: 0 !important;
  cursor: pointer !important;
  opacity: 0 !important;
  z-index: 3 !important;
  top: 0 !important;
  left: 0 !important;
  appearance: none !important;
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
}

.checkbox-custom {
  position: relative !important;
  width: 24px !important;
  height: 24px !important;
  border: 2px solid #64748b !important;
  border-radius: 6px !important;
  background: #0e0923 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  overflow: hidden !important;
  margin-right: 12px !important;
  flex-shrink: 0 !important;
  box-sizing: border-box !important;
}

.checkbox-custom::before {
  content: '' !important;
  position: absolute !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(135deg, #9333ea 0%, #a855f7 100%) !important;
  transform: scale(0) rotate(45deg) !important;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
  border-radius: 3px !important;
  z-index: 0 !important;
}

.checkbox-custom::after {
  content: '' !important;
  position: absolute !important;
  width: 8px !important;
  height: 14px !important;
  border: 2px solid white !important;
  border-top: none !important;
  border-left: none !important;
  transform: rotate(45deg) scale(0) !important;
  transition: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
  transition-delay: 0.1s !important;
  z-index: 1 !important;
}

.checkbox-custom:hover {
  border-color: #9333ea !important;
  box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1) !important;
  transform: scale(1.05) !important;
}

.addon-checkbox:checked + .checkbox-custom {
  border-color: #9333ea !important;
  background: #0e0923 !important;
  box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.2) !important;
}

.addon-checkbox:checked + .checkbox-custom::before {
  transform: scale(1) rotate(45deg) !important;
}

.addon-checkbox:checked + .checkbox-custom::after {
  transform: rotate(45deg) scale(1) !important;
}

.addon-checkbox:checked + .checkbox-custom {
  animation: checkboxPulse 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
}

@keyframes checkboxPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Force hide any default checkboxes in add-ons */
.addons-card input[type="checkbox"] {
  position: absolute !important;
  opacity: 0 !important;
  width: 24px !important;
  height: 24px !important;
  margin: 0 !important;
  z-index: 10 !important;
  cursor: pointer !important;
}
