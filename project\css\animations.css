/* Animation styles */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Apply animations to elements */
.fade-in {
  animation: fadeIn 1s ease forwards;
}

.slide-up {
  animation: slideUp 0.8s ease forwards;
}

.slide-down {
  animation: slideDown 0.8s ease forwards;
}

.slide-in-left {
  animation: slideInLeft 0.8s ease forwards;
}

.slide-in-right {
  animation: slideInRight 0.8s ease forwards;
}

.pulse {
  animation: pulse 2s infinite;
}

/* Staggered animation for grid items */
.services-grid .service-card,
.features-grid .feature-card,
.portfolio-grid .portfolio-item,
.pricing-grid .pricing-card {
  opacity: 0;
  transform: translateY(20px);
}

.animate-on-scroll {
  opacity: 1;
  transform: translateY(0);
  transition: 
    opacity 0.8s ease,
    transform 0.8s ease;
}