/* Contact section styles */
.contact {
  background-color: var(--color-bg);
  position: relative;
}

/* Conditional form field styles */
#current-website-group {
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

#current-website-group.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Error state for conditional fields */
.field-error {
  color: var(--color-error);
  font-size: 1.3rem;
  margin-top: var(--space-1);
  display: block;
}

.form-group input.error,
.form-group select.error {
  border-color: var(--color-error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.contact-info {
  padding: var(--space-5);
  background-color: var(--color-primary);
  color: white;
  border-radius: var(--border-radius-md);
  height: 100%;
}

.contact-info h3 {
  font-size: 2.4rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-3);
}

.contact-info p {
  margin-bottom: var(--space-5);
  opacity: 0.9;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.contact-method {
  display: flex;
  align-items: flex-start;
}

.method-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-md);
  background-color: rgba(255, 255, 255, 0.1);
  margin-right: var(--space-3);
  flex-shrink: 0;
}

.method-details h4 {
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-1);
}

.method-details a {
  color: rgba(255, 255, 255, 0.9);
  transition: color var(--transition-fast);
}

.method-details a:hover {
  color: white;
  text-decoration: underline;
}

.contact-free-audit {
  padding: var(--space-4);
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-md);
}

.contact-free-audit h4 {
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-2);
}

.contact-free-audit p {
  font-size: 1.5rem;
  margin-bottom: var(--space-3);
}

.contact-form-container {
  background-color: var(--color-bg);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  padding: var(--space-5);
  border: 1px solid var(--color-border);
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: var(--space-2);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: var(--space-3);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  background-color: var(--color-bg);
  color: var(--color-text);
  transition: all var(--transition-normal);
  font-size: 1.6rem;
}

/* Style the select dropdown arrow */
.form-group select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%239333ea' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
  padding-right: 40px;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.form-submit {
  margin-top: var(--space-2);
}

.form-response {
  min-height: 24px;
  text-align: center;
}

.form-response.success {
  color: var(--color-success);
}

.form-response.error {
  color: var(--color-error);
}