/* Footer styles */
.footer {
  background-color: var(--color-text);
  color: var(--color-bg);
  padding: var(--space-7) 0 var(--space-4) 0;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
  padding-bottom: var(--space-6);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-brand {
  margin-bottom: var(--space-5);
}

.footer-logo {
  display: flex;
  align-items: center;
  font-weight: var(--font-weight-bold);
  font-size: 2.4rem;
  color: white;
  margin-bottom: var(--space-3);
}

.footer-tagline {
  opacity: 0.7;
  max-width: 30rem;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--space-6);
}

.footer-group h4 {
  color: white;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-3);
}

.footer-group ul {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.footer-group a {
  color: rgba(255, 255, 255, 0.7);
  transition: color var(--transition-fast);
}

.footer-group a:hover {
  color: white;
}

.footer-bottom {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: var(--space-5);
  gap: var(--space-4);
}

.footer-social {
  display: flex;
  gap: var(--space-4);
}

.footer-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  transition: background-color var(--transition-fast);
}

.footer-social a:hover {
  background-color: var(--color-primary);
}

.footer-copyright p {
  opacity: 0.7;
  font-size: 1.4rem;
}

@media (min-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr 2fr;
  }

  .footer-bottom {
    flex-direction: row;
    justify-content: space-between;
  }
}