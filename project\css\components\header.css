/* Header styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: var(--z-fixed);
  transition:
    background-color var(--transition-normal),
    padding var(--transition-normal),
    box-shadow var(--transition-normal);
  padding: var(--space-4) 0;
}

.header-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header.scrolled {
  background-color: var(--color-bg);
  box-shadow: var(--shadow-md);
  padding: var(--space-3) 0;
}

.logo a {
  display: flex;
  align-items: center;
  font-weight: var(--font-weight-bold);
  font-size: 2.4rem;
  color: var(--color-primary);
}

.logo-icon {
  margin-right: var(--space-2);
  font-size: 2.8rem;
}

.nav-desktop {
  display: none;
}

.nav-desktop ul {
  display: flex;
  gap: var(--space-6);
}

.nav-desktop a {
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
  position: relative;
  transition: color var(--transition-fast);
}

.nav-desktop a:hover {
  color: var(--color-primary);
}

.nav-desktop a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -4px;
  left: 0;
  background-color: var(--color-primary);
  transition: width var(--transition-normal);
}

.nav-desktop a:hover::after {
  width: 100%;
}

.desktop-only {
  display: none;
}

.menu-toggle {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 24px;
  height: 20px;
  cursor: pointer;
}

.menu-toggle .bar {
  width: 100%;
  height: 2px;
  background-color: var(--color-text);
  transition: all var(--transition-normal);
}

.menu-toggle.active .bar:nth-child(1) {
  transform: translateY(9px) rotate(45deg);
}

.menu-toggle.active .bar:nth-child(2) {
  opacity: 0;
}

.menu-toggle.active .bar:nth-child(3) {
  transform: translateY(-9px) rotate(-45deg);
}

.nav-mobile {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: var(--color-bg);
  box-shadow: var(--shadow-md);
  padding: var(--space-4);
  display: none;
  opacity: 0;
  transform: translateY(-10px);
  transition:
    opacity var(--transition-normal),
    transform var(--transition-normal);
}

.nav-mobile.active {
  display: block;
  opacity: 1;
  transform: translateY(0);
}

.nav-mobile ul {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.nav-mobile a {
  display: block;
  padding: var(--space-2) 0;
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);
}

.nav-mobile a:not(.btn):hover {
  color: var(--color-primary);
}

.nav-mobile .btn {
  margin-top: var(--space-3);
}

@media (min-width: 768px) {
  .nav-desktop {
    display: block;
  }

  .desktop-only {
    display: block;
  }

  .menu-toggle {
    display: none;
  }
}