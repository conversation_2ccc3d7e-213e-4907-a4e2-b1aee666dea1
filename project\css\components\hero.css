/* Hero section styles */
.hero {
  padding-top: calc(6rem + var(--space-9));
  padding-bottom: var(--space-9);
  background: linear-gradient(135deg, #f9fafb 0%, #eff6ff 100%);
  position: relative;
  overflow: hidden;
}

.hero-content {
  text-align: center;
  max-width: 70rem;
  margin: 0 auto var(--space-7) auto;
}

.hero h1 {
  font-size: 4rem;
  font-weight: var(--font-weight-bold);
  line-height: 1.2;
  margin-bottom: var(--space-4);
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.hero p {
  font-size: 2rem;
  color: var(--color-text-light);
  margin-bottom: var(--space-5);
  max-width: 60rem;
  margin-left: auto;
  margin-right: auto;
}

.hero-cta {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  justify-content: center;
  align-items: center;
}

.hero-image {
  width: 90%;
  height: 300px; /* Fixed height to prevent layout shift */
  margin: 0 auto;
  position: relative;
  contain: layout size; /* Contain the layout to prevent shifts */
}

.browser-mockup {
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  position: relative;
  background-color: #fff;
  transform: perspective(800px) rotateX(5deg);
  transition: transform var(--transition-normal);
  height: 100%; /* Fill the parent container */
  width: 100%;
}

.browser-mockup:hover {
  transform: perspective(800px) rotateX(0);
}

.browser-header {
  background-color: #f1f5f9;
  padding: var(--space-2);
  display: flex;
  align-items: center;
  height: 40px; /* Fixed height */
}

.browser-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 6px;
}

.browser-dot:nth-child(1) {
  background-color: #f87171;
}

.browser-dot:nth-child(2) {
  background-color: #facc15;
}

.browser-dot:nth-child(3) {
  background-color: #4ade80;
}

.browser-content {
  height: calc(100% - 40px); /* Subtract header height */
  background: linear-gradient(to bottom right, var(--color-primary-light) 0%, var(--color-secondary) 100%);
  background-size: 400% 400%;
  animation: gradient 8s ease infinite;
}

.shape-divider {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  overflow: hidden;
  line-height: 0;
}

.shape-divider svg {
  position: relative;
  display: block;
  width: calc(100% + 1.3px);
  height: 80px;
}

.shape-divider .shape-fill {
  fill: var(--color-bg);
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@media (min-width: 768px) {
  .hero h1 {
    font-size: 5.2rem;
  }

  .hero-cta {
    flex-direction: row;
  }

  .browser-content {
    height: 320px;
  }
}