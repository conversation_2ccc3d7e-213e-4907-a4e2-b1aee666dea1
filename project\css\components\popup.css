/* Popup Modal Styles */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.popup-overlay.active {
  opacity: 1;
  visibility: visible;
}

.popup-container {
  background-color: var(--color-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--color-border);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.8) translateY(20px);
  transition: all 0.3s ease;
}

.popup-overlay.active .popup-container {
  transform: scale(1) translateY(0);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-5);
  border-bottom: 1px solid var(--color-border);
  background: linear-gradient(135deg, var(--color-bg) 0%, var(--color-bg-secondary) 100%);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.popup-header h3 {
  font-size: 2.2rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin: 0;
}

.popup-close {
  background: none;
  border: none;
  color: var(--color-text-light);
  cursor: pointer;
  padding: var(--space-2);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-close:hover {
  background-color: var(--color-bg-secondary);
  color: var(--color-text);
}

.popup-close svg {
  width: 20px;
  height: 20px;
}

.popup-content {
  padding: var(--space-5);
}

.popup-content p {
  color: var(--color-text-light);
  font-size: 1.6rem;
  line-height: 1.5;
  margin-bottom: var(--space-4);
  text-align: center;
}

/* Audit Form Styles */
.audit-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.audit-form .form-group {
  display: flex;
  flex-direction: column;
}

.audit-form label {
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin-bottom: var(--space-2);
  font-size: 1.4rem;
}

.audit-form input {
  padding: var(--space-3);
  border: 2px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  background-color: var(--color-bg-secondary);
  color: var(--color-text);
  font-size: 1.6rem;
  transition: all var(--transition-fast);
}

.audit-form input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1);
}

.audit-form input::placeholder {
  color: var(--color-text-light);
}

.audit-form .form-submit {
  margin-top: var(--space-2);
}

.audit-form .btn {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-size: 1.6rem;
  font-weight: var(--font-weight-medium);
}

.audit-form .form-response {
  margin-top: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--border-radius-sm);
  text-align: center;
  font-size: 1.4rem;
  display: none;
}

.audit-form .form-response.success {
  background-color: rgba(34, 197, 94, 0.1);
  color: var(--color-success);
  border: 1px solid rgba(34, 197, 94, 0.2);
  display: block;
}

.audit-form .form-response.error {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--color-error);
  border: 1px solid rgba(239, 68, 68, 0.2);
  display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
  .popup-container {
    width: 95%;
    margin: var(--space-4);
  }
  
  .popup-header {
    padding: var(--space-4);
  }
  
  .popup-header h3 {
    font-size: 1.8rem;
  }
  
  .popup-content {
    padding: var(--space-4);
  }
  
  .popup-content p {
    font-size: 1.4rem;
  }
  
  .audit-form input {
    padding: var(--space-2);
    font-size: 1.4rem;
  }
  
  .audit-form .btn {
    padding: var(--space-2) var(--space-3);
    font-size: 1.4rem;
  }
}

/* Animation for form submission */
.audit-form.submitting .btn {
  opacity: 0.7;
  cursor: not-allowed;
}

.audit-form.submitting .btn::after {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-left: var(--space-2);
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Accessibility improvements */
.popup-overlay[aria-hidden="true"] {
  pointer-events: none;
}

.popup-overlay[aria-hidden="false"] {
  pointer-events: auto;
}

/* Focus trap for accessibility */
.popup-container:focus {
  outline: none;
}

/* Smooth scrolling for popup content */
.popup-container {
  scroll-behavior: smooth;
}

.popup-container::-webkit-scrollbar {
  width: 6px;
}

.popup-container::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
}

.popup-container::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.popup-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-light);
}
