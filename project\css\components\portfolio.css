/* Portfolio section styles */
.portfolio {
  background-color: var(--color-bg-secondary);
}

.portfolio-item {
  background-color: var(--color-bg);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.portfolio-item:hover {
  transform: translateY(-5px);
}

.portfolio-image {
  width: 100%;
  height: 220px;
  overflow: hidden;
}

.portfolio-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.portfolio-item:hover .portfolio-image img {
  transform: scale(1.05);
}

.portfolio-content {
  padding: var(--space-5);
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.portfolio-item h3 {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-2);
  color: var(--color-text);
}

.portfolio-category {
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-3);
  font-size: 1.4rem;
}

.portfolio-description {
  color: var(--color-text-light);
  margin-bottom: var(--space-4);
  flex-grow: 1;
}

@media (min-width: 768px) {
  .portfolio-image {
    height: 240px;
  }
}