/* Pricing section styles */
.pricing {
  background-color: var(--color-bg-secondary);
  position: relative;
}

.pricing-card {
  background-color: var(--color-bg);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border);
  transition: transform var(--transition-normal);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.pricing-card:hover {
  transform: translateY(-10px);
}

.pricing-card.featured {
  border-color: var(--color-primary);
  transform: scale(1.03);
  z-index: 1;
}

.pricing-card.featured:hover {
  transform: scale(1.03) translateY(-10px);
}

.pricing-tag {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--color-primary);
  color: white;
  font-size: 1.4rem;
  padding: var(--space-1) var(--space-3);
  border-bottom-left-radius: var(--border-radius-md);
  font-weight: var(--font-weight-medium);
}

.pricing-header {
  padding: var(--space-5);
  border-bottom: 1px solid var(--color-border);
  text-align: center;
}

.pricing-header h3 {
  font-size: 2.2rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-2);
  color: var(--color-text);
  line-height: 1.3;
}

.pricing-subtitle {
  font-size: 1.4rem;
  color: var(--color-text-light);
  margin-bottom: var(--space-3);
  font-weight: var(--font-weight-medium);
}

.pricing-price {
  font-size: 2.8rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-primary);
  margin-bottom: var(--space-3);
  line-height: 1.2;
}

.pricing-price .period {
  font-size: 1.4rem;
  font-weight: var(--font-weight-normal);
  color: var(--color-text-light);
  margin-left: var(--space-1);
}

.pricing-description {
  color: var(--color-text-light);
  font-size: 1.5rem;
  line-height: 1.4;
}

.pricing-features {
  padding: var(--space-5);
  flex-grow: 1;
}

.pricing-features h4 {
  font-size: 1.6rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--space-3);
}

.pricing-features ul {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.pricing-features li {
  display: flex;
  align-items: flex-start;
  color: var(--color-text);
  font-size: 1.4rem;
  line-height: 1.4;
}

.pricing-features svg {
  flex-shrink: 0;
  width: 18px;
  height: 18px;
  margin-right: var(--space-2);
  margin-top: 2px;
  color: var(--color-success);
}

.optional-hosting {
  background-color: var(--color-bg-secondary);
  padding: var(--space-4);
  border-radius: var(--border-radius-sm);
  margin-top: var(--space-6);
  border-top: 2px solid var(--color-border);
}

.optional-hosting p {
  margin: 0;
  font-size: 1.4rem;
  color: var(--color-text);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.optional-hosting svg {
  color: var(--color-primary);
  flex-shrink: 0;
}

.pricing-card a {
  display: block;
  text-align: center;
  margin: 0 var(--space-5) var(--space-5) var(--space-5);
}

.pricing-note {
  text-align: center;
  margin-top: var(--space-6);
  color: var(--color-text-light);
}

.pricing-note a {
  color: var(--color-primary);
  text-decoration: underline;
  transition: color var(--transition-fast);
}

.pricing-note a:hover {
  color: var(--color-primary-dark);
}

/* Add-ons Card Styling */
.addons-card {
  border: 2px solid var(--color-accent);
  background: linear-gradient(135deg, var(--color-bg) 0%, var(--color-bg-secondary) 100%);
}

.addons-card .pricing-header h3 {
  color: var(--color-accent);
}

.addons-card .pricing-price {
  color: var(--color-accent);
}

/* Responsive Grid Layout */
@media (min-width: 1200px) {
  .pricing-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-4);
  }
}

@media (min-width: 768px) and (max-width: 1199px) {
  .pricing-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
  }

  .addons-card {
    grid-column: 1 / -1;
    max-width: 600px;
    margin: 0 auto;
  }
}

@media (max-width: 767px) {
  .pricing-header h3 {
    font-size: 1.8rem;
  }

  .pricing-price {
    font-size: 2.4rem;
  }

  .optional-hosting {
    margin-top: var(--space-4);
    padding: var(--space-3);
  }
}

/* Plan Selection Styles */
.pricing-card.selected-plan {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.2);
  transform: translateY(-5px);
}

.pricing-card.selected-plan::before {
  content: "✓ SELECTED";
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: var(--color-primary);
  color: white;
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: 1.2rem;
  font-weight: var(--font-weight-bold);
  z-index: 2;
}

/* Aesthetic Animated Addon Checkboxes */
.addon-checkbox {
  position: relative;
  width: 24px;
  height: 24px;
  margin-right: var(--space-3);
  cursor: pointer;
  opacity: 0;
  z-index: 2;
}

.addon-checkbox + .checkbox-custom {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border: 2px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-bg-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.addon-checkbox + .checkbox-custom::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  transform: scale(0) rotate(45deg);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  border-radius: 3px;
}

.addon-checkbox + .checkbox-custom::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 14px;
  border: 2px solid white;
  border-top: none;
  border-left: none;
  transform: rotate(45deg) scale(0);
  transition: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transition-delay: 0.1s;
  z-index: 1;
}

/* Hover effects */
.addon-checkbox + .checkbox-custom:hover {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.1);
  transform: translateY(-50%) scale(1.05);
}

/* Checked state */
.addon-checkbox:checked + .checkbox-custom {
  border-color: var(--color-primary);
  background: var(--color-bg-secondary);
  box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.2);
}

.addon-checkbox:checked + .checkbox-custom::before {
  transform: scale(1) rotate(45deg);
}

.addon-checkbox:checked + .checkbox-custom::after {
  transform: rotate(45deg) scale(1);
}

/* Focus states for accessibility */
.addon-checkbox:focus + .checkbox-custom {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Animation for the entire checkbox container */
.addon-checkbox:checked + .checkbox-custom {
  animation: checkboxPulse 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes checkboxPulse {
  0% {
    transform: translateY(-50%) scale(1);
  }
  50% {
    transform: translateY(-50%) scale(1.1);
  }
  100% {
    transform: translateY(-50%) scale(1);
  }
}

/* Ripple effect on click */
.addon-checkbox + .checkbox-custom {
  position: relative;
  overflow: visible;
}

.addon-checkbox + .checkbox-custom::before {
  z-index: 0;
}

.addon-checkbox:active + .checkbox-custom::before {
  animation: ripple 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes ripple {
  0% {
    transform: scale(0) rotate(45deg);
    opacity: 1;
  }
  100% {
    transform: scale(2) rotate(45deg);
    opacity: 0;
  }
}

/* Ensure the list item has proper positioning for the custom checkbox */
.addons-card .pricing-features li {
  position: relative;
  padding-left: 0;
  display: flex;
  align-items: center;
}

/* Plan Selection Field in Contact Form */
.plan-selection-field {
  margin-bottom: var(--space-4);
}

.plan-selection-field label {
  display: block;
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin-bottom: var(--space-2);
  font-size: 1.6rem;
}

.selected-plan-display {
  background-color: var(--color-bg-secondary);
  border: 2px solid var(--color-primary);
  border-radius: var(--border-radius-md);
  padding: var(--space-4);
}

.plan-summary {
  background-color: var(--color-bg);
  padding: var(--space-3);
  border-radius: var(--border-radius-sm);
  margin-bottom: var(--space-3);
  border: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.plan-name {
  font-size: 1.6rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
}

.plan-price {
  font-size: 1.4rem;
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.selected-addons {
  margin-bottom: var(--space-3);
}

.addons-label {
  color: var(--color-text);
  margin-bottom: var(--space-2);
  font-size: 1.4rem;
  font-weight: var(--font-weight-medium);
}

.addons-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.addon-item {
  background-color: var(--color-bg);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--border-radius-sm);
  font-size: 1.2rem;
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.total-price {
  background-color: var(--color-primary);
  color: white;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--border-radius-sm);
  margin-bottom: var(--space-3);
  font-size: 1.4rem;
  text-align: center;
}

.change-plan-btn {
  width: 100%;
  padding: var(--space-2);
  font-size: 1.3rem;
}

.no-plan-selected {
  background-color: var(--color-bg-secondary);
  border: 2px dashed var(--color-border);
  border-radius: var(--border-radius-md);
  padding: var(--space-4);
  text-align: center;
}

.no-plan-selected p {
  font-size: 1.4rem;
  color: var(--color-text-light);
  margin-bottom: var(--space-3);
}

.no-plan-selected .btn {
  padding: var(--space-2) var(--space-4);
  font-size: 1.3rem;
}

/* Selection Notification */
.selection-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: var(--color-primary);
  color: white;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--border-radius-md);
  font-weight: var(--font-weight-medium);
  z-index: 1000;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}