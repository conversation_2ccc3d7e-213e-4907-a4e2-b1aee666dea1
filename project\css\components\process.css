/* Process section styles */
.process {
  background-color: var(--color-bg-secondary);
  position: relative;
}

.process-timeline {
  max-width: 80rem;
  margin: 0 auto var(--space-7) auto;
  position: relative;
}

.process-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 24px;
  width: 2px;
  background: linear-gradient(to bottom, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.process-step {
  position: relative;
  padding-left: 60px;
  margin-bottom: var(--space-6);
}

.process-step:last-child {
  margin-bottom: 0;
}

.process-number {
  position: absolute;
  left: 0;
  top: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: 2rem;
  z-index: 1;
}

.process-content h3 {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-2);
  color: var(--color-text);
}

.process-content p {
  color: var(--color-text-light);
}

.process-info {
  display: flex;
  justify-content: center;
}

.info-box {
  background-color: var(--color-bg);
  border-radius: var(--border-radius-md);
  padding: var(--space-4);
  box-shadow: var(--shadow-md);
  text-align: center;
}

.info-box h4 {
  font-size: 1.6rem;
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-2);
  color: var(--color-text-light);
}

.info-box p {
  color: var(--color-text);
}

@media (min-width: 768px) {
  .process-timeline::before {
    left: 50%;
    transform: translateX(-50%);
  }

  .process-step {
    padding-left: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .process-step:nth-child(odd) {
    flex-direction: row-reverse;
  }

  .process-number {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .process-content {
    width: 45%;
    padding: var(--space-4);
    background-color: var(--color-bg);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
    transition: transform var(--transition-normal);
  }

  .process-step:hover .process-content {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
  }
}