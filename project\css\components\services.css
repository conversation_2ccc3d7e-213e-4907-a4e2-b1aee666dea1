/* Services section styles */
.services {
  background-color: var(--color-bg);
  position: relative;
}

.service-card {
  background-color: var(--color-bg);
  border-radius: var(--border-radius-md);
  padding: var(--space-5);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-border);
  transition: 
    transform var(--transition-normal),
    box-shadow var(--transition-normal);
  position: relative;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform var(--transition-normal);
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--color-primary-light);
  background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-4);
  color: white;
  transition: transform var(--transition-normal);
}

.service-card:hover .service-icon {
  transform: scale(1.1);
}

.service-card h3 {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-3);
  color: var(--color-text);
}

.service-card p {
  color: var(--color-text-light);
  flex-grow: 1;
}