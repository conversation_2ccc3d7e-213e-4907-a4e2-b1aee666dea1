/* Testimonials section styles */
.testimonials {
  background-color: var(--color-bg);
  position: relative;
}

.testimonials .container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.testimonials-slider {
  max-width: 70rem;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  width: 100%;
}

.testimonial-track {
  display: flex;
  transition: transform var(--transition-normal);
  width: 100%;
}

.testimonial-slide {
  min-width: 100%;
  width: 100%;
  flex: 0 0 100%;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.testimonial-content {
  background-color: var(--color-bg);
  border-radius: var(--border-radius-md);
  padding: var(--space-5);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--color-border);
  transition: transform var(--transition-normal);
  width: 100%;
  max-width: 60rem;
  margin: 0 auto;
}

.testimonial-content:hover {
  transform: translateY(-5px);
}

.quote {
  font-size: 1.8rem;
  color: var(--color-text);
  margin-bottom: var(--space-4);
  position: relative;
  line-height: 1.6;
}

.quote::before {
  content: '"';
  font-size: 5rem;
  color: var(--color-primary-light);
  opacity: 0.2;
  position: absolute;
  top: -2rem;
  left: -1rem;
}

.testimonial-info {
  display: flex;
  align-items: center;
}

.testimonial-image {
  width: 50px !important;
  height: 50px !important;
  border-radius: 50%;
  margin-right: var(--space-3);
  object-fit: cover;
  max-width: 50px !important;
  max-height: 50px !important;
  display: block;
}

.testimonial-name {
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: 0.2rem;
}

.testimonial-position {
  font-size: 1.4rem;
  color: var(--color-text-light);
}

.testimonial-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: var(--space-5);
  position: relative;
  z-index: 2;
}

.control-prev, .control-next {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-bg);
  color: var(--color-text);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
  border: 1px solid var(--color-border);
}

.control-prev:hover, .control-next:hover {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.testimonial-dots {
  display: flex;
  gap: var(--space-2);
  margin: 0 var(--space-3);
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--color-border);
  transition: all var(--transition-fast);
}

.dot.active {
  background-color: var(--color-primary);
}

.dot:hover {
  background-color: var(--color-primary-light);
}