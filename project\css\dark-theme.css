/* Dark Theme CSS - Modern Purple Aesthetic */

:root {
  /* Override color variables with dark theme */
  --color-bg: #080514;
  --color-bg-secondary: #0e0923;
  --color-bg-tertiary: #14102c;

  --color-primary: #9333ea;
  --color-primary-light: #a855f7;
  --color-primary-dark: #7e22ce;
  --color-secondary: #6366f1;
  --color-secondary-light: #818cf8;
  --color-secondary-dark: #4f46e5;

  --color-text: #f8fafc;
  --color-text-light: #cbd5e1;
  --color-text-lighter: #94a3b8;
  --color-border: #2d2152;

  /* Shadow overrides for dark theme */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Global styles */
body {
  background: linear-gradient(135deg, var(--color-bg) 0%, #0c0720 100%);
  color: var(--color-text);
  font-family: 'Urbanist', sans-serif;
  letter-spacing: 0.2px;
  line-height: 1.6;
  position: relative;
}

/* Adjust font weights for Urbanist */
p {
  font-weight: 300;
}

strong, b, h3, h4, h5, h6 {
  font-weight: 600;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgdmlld0JveD0iMCAwIDQwIDQwIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxnIGZpbGw9IiNmZmZmZmYiIGZpbGwtb3BhY2l0eT0iMC4wMyI+PHBhdGggZD0iTTAgMGg0MHY0MEgwVjB6bTIwIDJhMTggMTggMCAxIDAgMCAzNiAxOCAxOCAwIDAgMCAwLTM2eiIvPjwvZz48L2c+PC9zdmc+');
  opacity: 0.3;
  z-index: -1;
  pointer-events: none;
}

/* Add a subtle noise texture */
body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAAAXNSR0IArs4c6QAABBpJREFUaEPtmttx2zAQRQ+UiBLsElyCXYJdglyCXYJcglyCXIJdglyCXYJdglWCNBNrBjsEQIL8SJYdZTLjiSVi97DYB/bBzOzJzOLZE4D/Iq21X0TkXVXfRGTvnNs3xVprv4vILxH5LSKfqvq5Wq3+NnFzY5oAWWvfRQQgALGYgLHWHkTkraq+i8ibiLwAEjBfALRarfYtYKYCsdZ+E5FPVf1zOp0OIrJT1Z8istUQVX0BkLX2ICJbQKjqRkS2zrldDXMRSAZxVNUNPFbVDTJQA5KBbAECGGQFUKq6c87tSjCXgLwAQlUBgUxUQQQYay2gALJR1Q1gVHXnnNvWYC4CsdYeReQVIKq6V9Wjc+5rDUYGgwwBBJlBdpAhwDjnkKlJQJANZAQZQVYAhQzVYKy1yAagAIUsAQyZcs7tSzCTgWQwyAYyg6wAqgYTYLLsAAiZQXYAhUw55/YlmIuAZDDIBjKDrACqBhNgkBVAITPIDqCQKefcrgQzGUgGg2wgM8gKoGowASbLDoCQGWQHUMiUc25fgpkMJINBNpAZZAVQNZgAk2UHQMgMsgMoZMo5tyvBXAQkg0E2kBlkBVA1mACTZQdAyAyyAyhkyjm3L8FMBpLBIBvIDLICqBpMgMmyAyBkBtkBFDLlnNuVYCYDyWCQDWQGWQFUDSbAZNkBEDKD7AAKmXLO7Uswk4FkMMgGMoOsAKoGE2Cy7AAImUF2AIVMOed2JZjJQDIYZAOZQVYAVYMJMFl2AITMIDuAQqacc/sSzGQgGQyyEWBUdaeqR+fcVw0mwGTZARCyE2Ccc8jUJCDIRoCJMKq6d84dayCttchGgFHVvXPuWIK5CCSDAUiEUdWDc+5rDUYGE2FUdeOc25ZgLgLJYAASYVR155z7WoORwUQYVd04534UYC4GksEAJMKo6t4597UGI4OJMKq6cc5tSzAXA8lgABJhVHXnnPtag5HBRBhV3TjntiWYi4FkMACJMKq6c859rcHIYCKMqm6cc9sSzMVAMhiARBhV3TnnvtZgZDARRlU3zrltCeZiIBkMQCKMqu6cc19rMDKYCKOqG+fcj8nfWmutRTYCTIRR1b1z7liDaWQnwDjnkKlJQJCNABNhVHXvnDvWQFprkY0Ao6p759yxBHMRSAYDkAijqgfn3NcajAwmwqjqxjm3LcFcBJLBACTCqOrOOfe1BiODiTCqunHO/SjAXAwkgwFIhFHVnXPuaw1GBhNhVHXjnNuWYC4GksEAJMKo6s4597UGI4OJMKq6cc5tSzAXA8lgABJhVHXnnPtag5HBRBhV3TjntiWYi4FkMACJMKq6c859rcHIYCKMqm6cc9sSzMVAMhiARBhV3TnnvtZgZDARRlU3zrkfk7+1/gHVWYrJBUKSCwAAAABJRU5ErkJggg==');
  opacity: 0.02;
  z-index: -1;
  pointer-events: none;
}

/* Header styles */
.header {
  background-color: rgba(14, 10, 31, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.header.scrolled {
  background-color: rgba(14, 10, 31, 0.95);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.logo a {
  color: var(--color-primary-light);
}

.nav-desktop a {
  color: var(--color-text);
}

.nav-desktop a:hover {
  color: var(--color-primary-light);
}

.menu-toggle .bar {
  background-color: var(--color-text);
}

.nav-mobile {
  background-color: var(--color-bg-secondary);
}

/* Button styles */
.btn {
  border-radius: 8px;
  font-family: 'Urbanist', sans-serif;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  color: white;
  border: none;
  box-shadow: 0 4px 10px rgba(147, 51, 234, 0.3);
}

.btn-primary:hover {
  box-shadow: 0 6px 15px rgba(147, 51, 234, 0.5);
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: var(--color-primary-light);
  color: var(--color-primary-light);
}

.btn-outline {
  background-color: transparent;
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.btn-outline:hover {
  border-color: var(--color-primary-light);
  color: var(--color-primary-light);
}

.btn-gradient {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  color: white;
  border: none;
  box-shadow: 0 4px 10px rgba(147, 51, 234, 0.3);
}

.btn-gradient:hover {
  box-shadow: 0 6px 15px rgba(147, 51, 234, 0.5);
  transform: translateY(-2px);
}

/* Hero section */
.hero {
  background: transparent;
}

.hero h1 {
  font-family: 'Bebas Neue', sans-serif;
  background: linear-gradient(90deg, var(--color-primary-light) 0%, var(--color-secondary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  letter-spacing: 2px;
  line-height: 1.1;
  text-transform: uppercase;
}

/* Section styles */
section {
  background: transparent;
  position: relative;
  z-index: 1;
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-7);
}

.section-header h2 {
  font-family: 'Bebas Neue', sans-serif;
  font-size: 3.6rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-3);
  background: linear-gradient(90deg, var(--color-primary-light) 0%, var(--color-secondary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  letter-spacing: 1.5px;
  text-transform: uppercase;
}

.section-tag {
  display: inline-block;
  padding: 0.5rem 1.5rem;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.2) 0%, rgba(99, 102, 241, 0.2) 100%);
  color: var(--color-primary-light);
  border-radius: 50px;
  font-size: 1.4rem;
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-3);
  letter-spacing: 1px;
}

/* Card styles */
.service-card,
.feature-card,
.portfolio-item,
.pricing-card,
.contact-form-container {
  background-color: rgba(28, 20, 54, 0.5);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-content {
  background-color: rgba(28, 20, 54, 0.5);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  width: 100%;
  max-width: 60rem;
  margin: 0 auto;
}

.testimonial-content:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  border-color: var(--color-primary-light);
}

.service-card:hover,
.feature-card:hover,
.portfolio-item:hover,
.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
  border-color: var(--color-primary-light);
}

/* Pricing cards */
.pricing-card {
  display: flex;
  flex-direction: column;
  padding: var(--space-5) 0;
  position: relative;
  overflow: hidden;
  background: rgba(28, 20, 54, 0.4);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(99, 102, 241, 0.1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.pricing-card.featured {
  border-color: rgba(147, 51, 234, 0.3);
  box-shadow: 0 15px 35px rgba(147, 51, 234, 0.3);
  background: linear-gradient(135deg, rgba(28, 20, 54, 0.6) 0%, rgba(45, 33, 82, 0.6) 100%);
}

.pricing-tag {
  position: absolute;
  top: 12px;
  right: -30px;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
  color: white;
  padding: 0.5rem 3rem;
  font-size: 1.2rem;
  font-weight: var(--font-weight-bold);
  transform: rotate(45deg);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.pricing-header {
  text-align: center;
  padding: 0 var(--space-5);
  position: relative;
}

.pricing-header h3 {
  font-size: 2.2rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text);
  margin-bottom: var(--space-2);
}

.pricing-price {
  font-size: 4rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-primary-light);
  margin: var(--space-2) 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pricing-price + .period {
  font-size: 1.4rem;
  color: var(--color-text-lighter);
  margin-left: var(--space-1);
}

.pricing-features ul {
  position: relative;
}

.pricing-features li {
  position: relative;
  z-index: 1;
}

.pricing-features li svg {
  color: var(--color-primary-light);
}

.pricing-card .btn {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.pricing-card .btn-gradient::before,
.pricing-card .btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
}

.pricing-card .btn-gradient:hover::before,
.pricing-card .btn-primary:hover::before {
  left: 100%;
}

/* Footer */
.footer {
  background-color: var(--color-bg-secondary);
  border-top: 1px solid var(--color-border);
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: -100px;
  left: -100px;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(147, 51, 234, 0.1) 0%, rgba(147, 51, 234, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

.footer::after {
  content: '';
  position: absolute;
  bottom: -100px;
  right: -100px;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, rgba(99, 102, 241, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

.footer-content {
  position: relative;
  z-index: 1;
}

/* Glow effects */
.glow-effect {
  position: relative;
}

.glow-effect::before {
  content: '';
  position: absolute;
  top: -50px;
  left: -50px;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(147, 51, 234, 0.5) 0%, rgba(147, 51, 234, 0) 70%);
  border-radius: 50%;
  z-index: -1;
  opacity: 0.7;
  filter: blur(30px);
  animation: glow-pulse 10s ease-in-out infinite;
}

.glow-effect::after {
  content: '';
  position: absolute;
  bottom: -50px;
  right: -50px;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.5) 0%, rgba(99, 102, 241, 0) 70%);
  border-radius: 50%;
  z-index: -1;
  opacity: 0.7;
  filter: blur(30px);
  animation: glow-pulse 10s ease-in-out infinite reverse;
}

/* Add glow effects to sections */
.pricing {
  position: relative;
  overflow: hidden;
}

.pricing::before {
  content: '';
  position: absolute;
  top: -150px;
  left: -150px;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(147, 51, 234, 0.15) 0%, rgba(147, 51, 234, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

.pricing::after {
  content: '';
  position: absolute;
  bottom: -150px;
  right: -150px;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, rgba(99, 102, 241, 0.15) 0%, rgba(99, 102, 241, 0) 70%);
  border-radius: 50%;
  z-index: 0;
}

/* Clients section */
.clients {
  padding: var(--space-9) 0;
  position: relative;
  overflow: hidden;
}

.clients .section-title {
  font-family: 'Bebas Neue', sans-serif;
  text-align: center;
  font-size: 3.2rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-7);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  background: linear-gradient(90deg, var(--color-primary-light) 0%, var(--color-secondary-light) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  letter-spacing: 1.5px;
  text-transform: uppercase;
}

.clients-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--space-6);
  margin-top: var(--space-7);
}

@media (max-width: 768px) {
  .clients-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.client-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
  background: rgba(28, 20, 54, 0.3);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.client-logo:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  border-color: var(--color-primary-light);
}

.client-logo img {
  max-width: 100%;
  height: auto;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.client-logo:hover img {
  opacity: 1;
}

/* FAQ section */
.faq {
  padding: var(--space-9) 0;
  position: relative;
  overflow: hidden;
}

.faq-container {
  display: grid;
  grid-template-columns: 1fr 3fr;
  gap: var(--space-6);
  margin-top: var(--space-7);
}

@media (max-width: 768px) {
  .faq-container {
    grid-template-columns: 1fr;
  }
}

.faq-cta {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.faq-item {
  background: rgba(28, 20, 54, 0.4);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-item:hover {
  border-color: var(--color-primary-light);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.faq-question {
  padding: var(--space-4);
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.faq-question h3 {
  font-size: 1.8rem;
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
  margin: 0;
}

.faq-icon {
  font-size: 2.4rem;
  color: var(--color-primary-light);
  transition: transform 0.3s ease;
}

.faq-item.active .faq-icon {
  transform: rotate(45deg);
}

.faq-answer {
  padding: 0 var(--space-4) var(--space-4) var(--space-4);
  display: none;
}

.faq-item.active .faq-answer {
  display: block;
}

.faq-answer p {
  color: var(--color-text-light);
  margin: 0;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary-dark);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

/* Selection styling */
::selection {
  background-color: var(--color-primary);
  color: white;
}

/* Background glow effects */
.glow-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
  pointer-events: none;
}

.glow-element {
  position: absolute;
  border-radius: 50%;
  filter: blur(90px);
  opacity: 0.9; /* Increased opacity for better visibility */
  transform: translate(-50%, -50%);
  mix-blend-mode: screen;
  animation: glow-pulse 8s ease-in-out infinite;
  will-change: opacity, transform;
  transition: opacity 0.8s ease;
}

.glow-element.blue {
  filter: blur(110px);
  animation-delay: 2s;
  animation-duration: 10s; /* Slightly longer duration for blue */
}

.glow-element.purple {
  filter: blur(90px);
  animation-delay: 0s;
  animation-duration: 8s;
}

.glow-element.static {
  opacity: 0.65; /* Increased opacity for static elements */
  filter: blur(80px); /* Reduced blur for sharper edges */
  animation: none; /* Static elements still don't animate */
}

/* Class added by JS to disable animations */
.paused-animations .glow-element {
  animation: none !important;
  transition: none !important;
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .glow-element {
    animation: none;
  }

  .fade-in {
    animation: none !important;
    opacity: 1 !important;
  }
}

/* Class for JS to add when reduced motion is detected */
.reduce-motion .glow-element {
  animation: none;
}

.reduce-motion .fade-in {
  animation: none !important;
  opacity: 1 !important;
}

/* Glow pulse animation */
@keyframes glow-pulse {
  0% {
    opacity: 0.5; /* Increased minimum opacity */
    transform: translate(-50%, -50%) scale(0.9);
  }
  50% {
    opacity: 0.8; /* Increased maximum opacity */
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0.5; /* Increased minimum opacity */
    transform: translate(-50%, -50%) scale(0.9);
  }
}

/* Contact form styling for dark theme */
.form-group input,
.form-group select,
.form-group textarea {
  background-color: rgba(45, 33, 82, 0.4);
  border: 1px solid rgba(147, 51, 234, 0.3);
  color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
  background-color: rgba(45, 33, 82, 0.5);
  border-color: rgba(147, 51, 234, 0.4);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  background-color: rgba(45, 33, 82, 0.6);
  border-color: rgba(147, 51, 234, 0.6);
  box-shadow: 0 0 0 2px rgba(147, 51, 234, 0.2), 0 4px 6px rgba(0, 0, 0, 0.1);
  color: white;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-group select {
  color: rgba(255, 255, 255, 0.7);
}

.form-group select option {
  background-color: rgba(28, 20, 54, 0.95);
  color: rgba(255, 255, 255, 0.9);
}

/* Add a subtle noise texture overlay to the entire page */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  opacity: 0.015;
  z-index: -1;
  pointer-events: none;
}
