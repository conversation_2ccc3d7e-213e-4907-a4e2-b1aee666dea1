/* Layout styles */
.container {
  width: 100%;
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-4);
}

section {
  padding: var(--space-8) 0;
  position: relative;
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-7);
}

.section-header h2 {
  font-size: 3.6rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-2);
  color: var(--color-text);
}

.section-header p {
  font-size: 1.8rem;
  color: var(--color-text-light);
  max-width: 60rem;
  margin: 0 auto;
}

/* Responsive grid layouts */
.services-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--space-5);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--space-5);
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--space-6);
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: var(--space-4);
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-6);
}

/* Media queries for responsive layouts */
@media (min-width: 640px) {
  .services-grid,
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  section {
    padding: var(--space-9) 0;
  }

  .portfolio-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .services-grid,
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .pricing-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .contact-grid {
    grid-template-columns: 1fr 1fr;
  }
}