/* CSS Variables */
:root {
  /* Colors */
  --color-primary: #4f46e5;
  --color-primary-light: #6366f1;
  --color-primary-dark: #4338ca;
  --color-secondary: #0ea5e9;
  --color-secondary-light: #38bdf8;
  --color-secondary-dark: #0284c7;
  --color-accent: #f97316;
  --color-accent-light: #fb923c;
  --color-accent-dark: #ea580c;

  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;

  --color-bg: #ffffff;
  --color-bg-secondary: #f9fafb;
  --color-bg-tertiary: #f3f4f6;

  --color-text: #1e293b;
  --color-text-light: #64748b;
  --color-text-lighter: #94a3b8;
  --color-border: #e2e8f0;

  /* Spacing */
  --space-1: 0.4rem;
  --space-2: 0.8rem;
  --space-3: 1.2rem;
  --space-4: 1.6rem;
  --space-5: 2.4rem;
  --space-6: 3.2rem;
  --space-7: 4.0rem;
  --space-8: 4.8rem;
  --space-9: 6.4rem;
  --space-10: 8.0rem;

  /* Typography */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;

  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-loose: 1.8;

  /* Borders */
  --border-radius-sm: 0.4rem;
  --border-radius-md: 0.8rem;
  --border-radius-lg: 1.2rem;
  --border-radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms ease;
  --transition-slow: 500ms ease;

  /* Container widths */
  --container-sm: 54rem;
  --container-md: 76.8rem;
  --container-lg: 102.4rem;
  --container-xl: 120rem;

  /* Z-index levels */
  --z-negative: -1;
  --z-low: 1;
  --z-mid: 5;
  --z-high: 10;
  --z-modal: 50;
  --z-fixed: 100;
}