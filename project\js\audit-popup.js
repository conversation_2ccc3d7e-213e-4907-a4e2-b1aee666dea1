// Free Audit Popup Management
class AuditPopup {
  constructor() {
    this.popup = null;
    this.auditBtn = null;
    this.closeBtn = null;
    this.form = null;
    this.isOpen = false;
    this.init();
  }

  init() {
    this.popup = document.getElementById('audit-popup');
    this.auditBtn = document.getElementById('audit-btn');
    this.closeBtn = this.popup?.querySelector('.popup-close');
    this.form = document.getElementById('audit-form');

    if (!this.popup || !this.auditBtn || !this.closeBtn || !this.form) {
      console.warn('Audit popup elements not found');
      return;
    }

    this.bindEvents();
    this.setupAccessibility();
  }

  bindEvents() {
    // Open popup when audit button is clicked
    this.auditBtn.addEventListener('click', (e) => {
      e.preventDefault();
      this.openPopup();
    });

    // Close popup when close button is clicked
    this.closeBtn.addEventListener('click', (e) => {
      e.preventDefault();
      this.closePopup();
    });

    // Close popup when clicking outside the container
    this.popup.addEventListener('click', (e) => {
      if (e.target === this.popup) {
        this.closePopup();
      }
    });

    // Close popup on Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.closePopup();
      }
    });

    // Handle form submission
    this.form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleFormSubmission();
    });

    // Prevent form submission on Enter in input fields (except submit button)
    this.form.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && e.target.tagName !== 'BUTTON') {
        e.preventDefault();
        // Move to next field or submit if it's the last field
        const inputs = Array.from(this.form.querySelectorAll('input[type="text"], input[type="email"], input[type="url"], input[type="tel"]'));
        const currentIndex = inputs.indexOf(e.target);
        if (currentIndex < inputs.length - 1) {
          inputs[currentIndex + 1].focus();
        } else {
          this.form.querySelector('button[type="submit"]').click();
        }
      }
    });
  }

  setupAccessibility() {
    // Set initial ARIA attributes
    this.popup.setAttribute('aria-hidden', 'true');
    this.popup.setAttribute('role', 'dialog');
    this.popup.setAttribute('aria-labelledby', 'audit-popup-title');
    this.popup.setAttribute('aria-modal', 'true');

    // Add ID to title for aria-labelledby
    const title = this.popup.querySelector('h3');
    if (title) {
      title.id = 'audit-popup-title';
    }
  }

  openPopup() {
    this.isOpen = true;
    this.popup.classList.add('active');
    this.popup.setAttribute('aria-hidden', 'false');

    // Prevent body scroll
    document.body.style.overflow = 'hidden';

    // Focus management
    this.trapFocus();

    // Focus first input
    const firstInput = this.form.querySelector('input[type="text"]');
    if (firstInput) {
      setTimeout(() => firstInput.focus(), 100);
    }

    // Track event (if analytics is available)
    if (typeof gtag !== 'undefined') {
      gtag('event', 'popup_open', {
        'popup_type': 'audit_request'
      });
    }
  }

  closePopup() {
    this.isOpen = false;
    this.popup.classList.remove('active');
    this.popup.setAttribute('aria-hidden', 'true');

    // Restore body scroll
    document.body.style.overflow = '';

    // Return focus to trigger button
    this.auditBtn.focus();

    // Clear form
    this.clearForm();

    // Remove any error/success messages
    this.clearFormResponse();
  }

  trapFocus() {
    const focusableElements = this.popup.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleTabKey = (e) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    // Remove existing listener if any
    this.popup.removeEventListener('keydown', this.tabHandler);
    this.tabHandler = handleTabKey;
    this.popup.addEventListener('keydown', this.tabHandler);
  }

  async handleFormSubmission() {
    const formData = new FormData(this.form);
    const data = Object.fromEntries(formData.entries());

    // Basic validation
    if (!this.validateForm(data)) {
      return;
    }

    // Show loading state
    this.setFormLoading(true);

    try {
      // Submit form to backend API
      const result = await this.submitForm(data);
      this.showFormResponse('success', result.message || 'Thank you! We\'ll send your free audit within 24 hours.');

      // Track successful submission
      if (typeof gtag !== 'undefined') {
        gtag('event', 'form_submit', {
          'form_type': 'audit_request'
        });
      }

      // Close popup after delay
      setTimeout(() => {
        this.closePopup();
      }, 2000);

    } catch (error) {
      console.error('Form submission error:', error);
      this.showFormResponse('error', error.message || 'Something went wrong. Please try again or contact us directly.');
    } finally {
      this.setFormLoading(false);
    }
  }

  validateForm(data) {
    const errors = [];

    if (!data.name || data.name.trim().length < 2) {
      errors.push('Please enter your full name');
    }

    if (!data.email || !this.isValidEmail(data.email)) {
      errors.push('Please enter a valid email address');
    }

    if (!data.website || !this.isValidUrl(data.website)) {
      errors.push('Please enter a valid website URL');
    }

    if (errors.length > 0) {
      this.showFormResponse('error', errors.join('<br>'));
      return false;
    }

    return true;
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  async submitForm(data) {
    try {
      // Wait for email service to be available
      if (!window.emailService) {
        throw new Error('Email service not initialized');
      }

      const result = await window.emailService.sendAuditRequest(data);

      if (!result.success) {
        throw new Error(result.message);
      }

      return result;
    } catch (error) {
      throw error;
    }
  }

  setFormLoading(loading) {
    const submitBtn = this.form.querySelector('button[type="submit"]');
    const inputs = this.form.querySelectorAll('input');

    if (loading) {
      this.form.classList.add('submitting');
      submitBtn.disabled = true;
      submitBtn.textContent = 'Sending...';
      inputs.forEach(input => input.disabled = true);
    } else {
      this.form.classList.remove('submitting');
      submitBtn.disabled = false;
      submitBtn.textContent = 'Request Free Audit';
      inputs.forEach(input => input.disabled = false);
    }
  }

  showFormResponse(type, message) {
    const responseDiv = this.form.querySelector('.form-response');
    responseDiv.className = `form-response ${type}`;
    responseDiv.innerHTML = message;
    responseDiv.style.display = 'block';
  }

  clearFormResponse() {
    const responseDiv = this.form.querySelector('.form-response');
    responseDiv.style.display = 'none';
    responseDiv.innerHTML = '';
  }

  clearForm() {
    this.form.reset();
    this.clearFormResponse();
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new AuditPopup();
});
