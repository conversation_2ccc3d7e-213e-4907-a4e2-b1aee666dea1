// Animations functionality - treating sections as whole units with special Mexican wave for services
(function() {
  // Wait for DOM to be fully loaded
  document.addEventListener('DOMContentLoaded', function() {
    // Mark sections for animation
    prepareSectionsForAnimation();

    // Check if user prefers reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    if (prefersReducedMotion) {
      // Skip animations for users who prefer reduced motion
      document.body.classList.add('reduce-motion');
      // Make all sections visible immediately
      document.querySelectorAll('section[data-animate]').forEach(section => {
        section.style.opacity = '1';
        section.style.visibility = 'visible';
        section.style.transform = 'translateY(0)';
      });
      // Make all service cards visible immediately
      document.querySelectorAll('.services .service-card').forEach(card => {
        card.style.opacity = '1';
        card.style.transform = 'translateY(0)';
      });
      return;
    }

    // Initialize animations after a short delay to ensure everything is ready
    setTimeout(() => {
      // Animate hero section immediately
      animateHeroSection();

      // Set up scroll animations for sections
      initSectionAnimations();
    }, 100);
  });

  // Mark sections for animation by adding data-animate attribute
  function prepareSectionsForAnimation() {
    // Get all sections except the hero section
    const sections = document.querySelectorAll('section:not(.hero)');

    // Add data-animate attribute to all sections
    sections.forEach(section => {
      section.setAttribute('data-animate', 'true');
    });

    // Prepare service cards for the Mexican wave effect
    const serviceCards = document.querySelectorAll('.services .service-card');
    serviceCards.forEach(card => {
      // No data-animate attribute needed for cards as they'll be animated by their parent section
    });
  }

  // Animate the hero section immediately
  function animateHeroSection() {
    const heroElements = document.querySelectorAll('.hero h1, .hero p, .hero-cta');

    // Apply animations with delay
    heroElements.forEach((element, index) => {
      setTimeout(() => {
        element.classList.add('fade-in');
      }, index * 200);
    });
  }

  // Initialize scroll animations for entire sections
  function initSectionAnimations() {
    const sections = document.querySelectorAll('section[data-animate]');

    // Create an intersection observer for sections
    const sectionObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        // Only animate section when it enters the viewport
        if (entry.isIntersecting) {
          const section = entry.target;

          // Add animation class to the section
          section.classList.add('fade-in');

          // If this is the services section, animate the cards with a vertical Mexican wave effect
          if (section.classList.contains('services')) {
            animateServiceCards(section);
          }

          // If this is the process section, animate the steps with a horizontal Mexican wave effect
          if (section.classList.contains('process')) {
            animateProcessSteps(section);
          }

          // Stop observing this section
          observer.unobserve(section);
        }
      });
    }, {
      root: null, // Use the viewport
      rootMargin: '0px 0px -15% 0px', // Trigger when section is 15% into the viewport
      threshold: 0.01 // Trigger when just a tiny bit of the section is visible
    });

    // Start observing all sections
    sections.forEach(section => {
      sectionObserver.observe(section);
    });
  }

  // Animate service cards with a vertical Mexican wave effect
  function animateServiceCards(servicesSection) {
    // Get all service cards
    const serviceCards = servicesSection.querySelectorAll('.service-card');

    // Make sure all cards are initially in the correct position but hidden
    serviceCards.forEach(card => {
      card.style.opacity = '0';
      card.style.transform = 'translateY(0)'; // Start at correct position
      card.style.visibility = 'hidden';
    });

    // Add wave-animate class to each card to trigger the Mexican wave animation
    // The animation will handle the movement up and then back down
    setTimeout(() => {
      serviceCards.forEach(card => {
        card.classList.add('wave-animate');
      });
    }, 200); // Slightly longer delay to ensure section is fully visible first
  }

  // Animate process steps with a horizontal Mexican wave effect
  function animateProcessSteps(processSection) {
    // Get all process steps
    const processSteps = processSection.querySelectorAll('.process-step');

    // Make sure all steps are initially in the correct position but hidden
    processSteps.forEach(step => {
      step.style.opacity = '0';
      step.style.transform = 'translateX(-50px)'; // Start left of correct position
      step.style.visibility = 'hidden';
    });

    // Add wave-animate class to each step with a slight delay to ensure proper wave effect
    // The animation will handle the movement right and then back to original position
    setTimeout(() => {
      processSteps.forEach(step => {
        step.classList.add('wave-animate');
      });
    }, 200); // Slightly longer delay to ensure section is fully visible first
  }
})();