// Contact form functionality
const contactForm = document.getElementById('contact-form');
const formResponse = document.querySelector('.form-response');

// Validate email format
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Form submission handler
function handleFormSubmit(event) {
  event.preventDefault();
  
  // Get form data
  const formData = new FormData(contactForm);
  const formValues = Object.fromEntries(formData.entries());
  
  // Basic validation
  if (!formValues.name || !formValues.email || !formValues['project-type'] || !formValues.message) {
    showFormResponse('Please fill out all required fields.', 'error');
    return;
  }
  
  if (!isValidEmail(formValues.email)) {
    showFormResponse('Please enter a valid email address.', 'error');
    return;
  }
  
  // Simulate form submission (would be replaced with actual API call)
  simulateFormSubmission(formValues);
}

// Show form response message
function showFormResponse(message, type) {
  formResponse.textContent = message;
  formResponse.className = 'form-response ' + type;
  
  // Clear message after 5 seconds
  setTimeout(() => {
    formResponse.textContent = '';
    formResponse.className = 'form-response';
  }, 5000);
}

// Simulate form submission (for demo purposes)
function simulateFormSubmission(formValues) {
  // Show loading state
  showFormResponse('Sending your message...', '');
  
  // Simulate API delay
  setTimeout(() => {
    console.log('Form submitted with values:', formValues);
    showFormResponse('Your message has been sent! We\'ll contact you soon.', 'success');
    contactForm.reset();
  }, 1500);
}

// Initialize form functionality
function initForm() {
  if (contactForm) {
    contactForm.addEventListener('submit', handleFormSubmit);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initForm);