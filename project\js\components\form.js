// Contact form functionality
const contactForm = document.getElementById('contact-form');
const formResponse = document.querySelector('.form-response');

// Validate email format
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Form submission handler
function handleFormSubmit(event) {
  event.preventDefault();

  // Get form data
  const formData = new FormData(contactForm);
  const formValues = Object.fromEntries(formData.entries());

  // Basic validation
  if (!formValues.name || !formValues.email || !formValues['project-type'] || !formValues.message) {
    showFormResponse('Please fill out all required fields.', 'error');
    return;
  }

  if (!isValidEmail(formValues.email)) {
    showFormResponse('Please enter a valid email address.', 'error');
    return;
  }

  // Submit form using EmailJS
  submitFormToEmailJS(formValues);
}

// Show form response message
function showFormResponse(message, type) {
  formResponse.textContent = message;
  formResponse.className = 'form-response ' + type;

  // Clear message after 5 seconds
  setTimeout(() => {
    formResponse.textContent = '';
    formResponse.className = 'form-response';
  }, 5000);
}

// Submit form using EmailJS
async function submitFormToEmailJS(formValues) {
  // Show loading state
  showFormResponse('Sending your message...', '');

  try {
    // Wait for email service to be available
    if (!window.emailService) {
      throw new Error('Email service not initialized');
    }

    const result = await window.emailService.sendContactForm(formValues);

    if (result.success) {
      showFormResponse(result.message, 'success');
      contactForm.reset();

      // Clear any selected plans/addons
      if (window.planSelection) {
        window.planSelection.clearSelection();
      }
    } else {
      showFormResponse(result.message, 'error');
    }
  } catch (error) {
    console.error('Form submission error:', error);
    showFormResponse('Something went wrong. Please try again or contact us directly.', 'error');
  }
}

// Initialize form functionality
function initForm() {
  if (contactForm) {
    contactForm.addEventListener('submit', handleFormSubmit);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initForm);