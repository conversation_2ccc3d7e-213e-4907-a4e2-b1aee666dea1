// Contact form functionality
const contactForm = document.getElementById('contact-form');
const formResponse = document.querySelector('.form-response');

// Validate email format
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Form submission handler
function handleFormSubmit(event) {
  event.preventDefault();

  // Get form data
  const formData = new FormData(contactForm);
  const formValues = Object.fromEntries(formData.entries());

  // Basic validation
  if (!formValues.name || !formValues.email || !formValues['project-type'] || !formValues.message) {
    showFormResponse('Please fill out all required fields.', 'error');
    return;
  }

  if (!isValidEmail(formValues.email)) {
    showFormResponse('Please enter a valid email address.', 'error');
    return;
  }

  // Submit form to backend API
  submitFormToAPI(formValues);
}

// Show form response message
function showFormResponse(message, type) {
  formResponse.textContent = message;
  formResponse.className = 'form-response ' + type;

  // Clear message after 5 seconds
  setTimeout(() => {
    formResponse.textContent = '';
    formResponse.className = 'form-response';
  }, 5000);
}

// Submit form to backend API
async function submitFormToAPI(formValues) {
  // Show loading state
  showFormResponse('Sending your message...', '');

  try {
    const response = await fetch('/api/contact', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formValues)
    });

    const result = await response.json();

    if (result.success) {
      showFormResponse(result.message, 'success');
      contactForm.reset();

      // Clear any selected plans/addons
      if (window.planSelection) {
        window.planSelection.clearSelection();
      }
    } else {
      // Handle validation errors
      if (result.errors && result.errors.length > 0) {
        const errorMessages = result.errors.map(error => error.msg).join(', ');
        showFormResponse(errorMessages, 'error');
      } else {
        showFormResponse(result.message || 'Something went wrong. Please try again.', 'error');
      }
    }
  } catch (error) {
    console.error('Form submission error:', error);
    showFormResponse('Network error. Please check your connection and try again.', 'error');
  }
}

// Initialize form functionality
function initForm() {
  if (contactForm) {
    contactForm.addEventListener('submit', handleFormSubmit);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initForm);