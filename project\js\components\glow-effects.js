// Enhanced Background Illumination System with Animations
(function() {
  // Create a placeholder container immediately to reserve space and prevent layout shifts
  function createPlaceholderContainer() {
    const glowContainer = document.createElement('div');
    glowContainer.className = 'glow-container';
    glowContainer.id = 'glow-container';
    glowContainer.style.opacity = '0'; // Start invisible
    document.body.insertBefore(glowContainer, document.body.firstChild);
    return glowContainer;
  }

  // Create placeholder immediately to prevent layout shifts
  const placeholderContainer = createPlaceholderContainer();

  // Initialize after DOM is ready but before full page load
  document.addEventListener('DOMContentLoaded', function() {
    // Check if device is low-end or prefers reduced motion
    const isMobile = window.matchMedia("(max-width: 768px)").matches;
    const prefersReducedMotion = window.matchMedia("(prefers-reduced-motion: reduce)").matches;

    // Choose appropriate illumination based on device capability
    if (isMobile || prefersReducedMotion) {
      initStaticIllumination(placeholderContainer);
      // Add class to disable animations for these devices
      document.body.classList.add('paused-animations');
    } else {
      initAnimatedIllumination(placeholderContainer);
    }

    // Fade in the glow container
    requestAnimationFrame(() => {
      placeholderContainer.style.transition = 'opacity 0.5s ease';
      placeholderContainer.style.opacity = '1';
    });
  });

  // Static illumination for mobile/low-end devices - 3 static glows
  function initStaticIllumination(container) {
    // Use the provided container instead of creating a new one
    const glowContainer = container || document.getElementById('glow-container');

    // Clear any existing content
    glowContainer.innerHTML = '';

    // 3 strategic static glows
    const staticGlows = [
      // Purple glows
      { x: 20, y: 15, size: 600, color: 'rgba(147, 51, 234, 0.55)', type: 'purple static' },
      { x: 75, y: 85, size: 600, color: 'rgba(168, 85, 247, 0.55)', type: 'purple static' },

      // Blue glow
      { x: 80, y: 20, size: 650, color: 'rgba(59, 130, 246, 0.55)', type: 'blue static' }
    ];

    // Create static glows
    createGlows(glowContainer, staticGlows);
    return glowContainer;
  }

  // Animated illumination for standard devices - 6 glows with animations
  function initAnimatedIllumination(container) {
    // Use the provided container instead of creating a new one
    const glowContainer = container || document.getElementById('glow-container');

    // Clear any existing content
    glowContainer.innerHTML = '';

    // Animated glows
    const animatedGlows = [
      // Purple glows
      { x: 20, y: 15, size: 600, color: 'rgba(147, 51, 234, 0.6)', type: 'purple' },
      { x: 75, y: 85, size: 600, color: 'rgba(168, 85, 247, 0.6)', type: 'purple' },
      { x: 40, y: 40, size: 500, color: 'rgba(147, 51, 234, 0.55)', type: 'purple' },

      // Blue glows
      { x: 80, y: 20, size: 700, color: 'rgba(59, 130, 246, 0.6)', type: 'blue' },
      { x: 25, y: 80, size: 650, color: 'rgba(37, 99, 235, 0.55)', type: 'blue' },
      { x: 60, y: 60, size: 550, color: 'rgba(96, 165, 250, 0.55)', type: 'blue' }
    ];

    // Create animated glows
    createGlows(glowContainer, animatedGlows);
    return glowContainer;
  }

  // Helper function to create glows
  function createGlows(container, glows) {
    // Use document fragment for better performance
    const fragment = document.createDocumentFragment();

    glows.forEach((glow, index) => {
      const element = document.createElement('div');
      element.className = `glow-element ${glow.type}`;

      // Set position and size
      element.style.left = `${glow.x}%`;
      element.style.top = `${glow.y}%`;
      element.style.width = `${glow.size}px`;
      element.style.height = `${glow.size}px`;

      // Set gradient background with extended spread for better visibility
      element.style.background = `radial-gradient(circle, ${glow.color} 0%, rgba(8, 5, 20, 0) 80%)`;

      // Add animation delay for animated elements
      if (!glow.type.includes('static')) {
        // Stagger animation delays
        element.style.animationDelay = `${index * 0.5}s`;
      }

      // Add to fragment
      fragment.appendChild(element);
    });

    // Add all elements at once
    container.appendChild(fragment);
  }
})();
