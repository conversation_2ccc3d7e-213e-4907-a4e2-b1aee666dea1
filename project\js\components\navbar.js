// Navbar functionality
const header = document.getElementById('header');
const menuToggle = document.querySelector('.menu-toggle');
const navMobile = document.querySelector('.nav-mobile');
const navLinks = document.querySelectorAll('.nav-mobile a');

// Toggle mobile menu
function toggleMenu() {
  menuToggle.classList.toggle('active');
  navMobile.classList.toggle('active');
}

// Handle scroll behavior for header
function handleScroll() {
  if (window.scrollY > 10) {
    header.classList.add('scrolled');
  } else {
    header.classList.remove('scrolled');
  }
}

// Initialize navbar functionality
function initNavbar() {
  // Add event listener for menu toggle
  menuToggle.addEventListener('click', toggleMenu);

  // Add event listeners for mobile nav links to close menu when clicked
  navLinks.forEach(link => {
    link.addEventListener('click', toggleMenu);
  });

  // Add scroll event listener
  window.addEventListener('scroll', handleScroll);

  // Initial check for scroll position
  handleScroll();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initNavbar);