// Scroll functionality
const scrollTopButton = document.getElementById('scroll-top');
const sections = document.querySelectorAll('section');
const animatedElements = document.querySelectorAll('.services-grid .service-card, .features-grid .feature-card, .portfolio-grid .portfolio-item, .pricing-grid .pricing-card');

// Scroll to top function
function scrollToTop() {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
}

// Toggle scroll to top button visibility
function toggleScrollTopButton() {
  if (window.scrollY > 500) {
    scrollTopButton.classList.add('visible');
  } else {
    scrollTopButton.classList.remove('visible');
  }
}

// Handle scroll animations
function handleScrollAnimations() {
  animatedElements.forEach(element => {
    const elementPosition = element.getBoundingClientRect().top;
    const windowHeight = window.innerHeight;
    
    if (elementPosition < windowHeight * 0.85) {
      element.classList.add('animate-on-scroll');
    }
  });
}

// Initialize scroll functionality
function initScroll() {
  // Add event listener for scroll to top button
  if (scrollTopButton) {
    scrollTopButton.addEventListener('click', scrollToTop);
  }

  // Add scroll event listeners
  window.addEventListener('scroll', () => {
    toggleScrollTopButton();
    handleScrollAnimations();
  });

  // Initial checks
  toggleScrollTopButton();
  handleScrollAnimations();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initScroll);