// Testimonials slider functionality
const testimonialTrack = document.querySelector('.testimonial-track');
const testimonialSlides = document.querySelectorAll('.testimonial-slide');
const prevButton = document.querySelector('.control-prev');
const nextButton = document.querySelector('.control-next');
const dots = document.querySelectorAll('.dot');

let currentSlide = 0;
let slideWidth = 0;
let autoplayInterval;
const autoplayDelay = 5000; // 5 seconds

// Update slide position
function updateSlidePosition() {
  testimonialTrack.style.transform = `translateX(-${currentSlide * slideWidth}px)`;

  // Update active dot
  dots.forEach((dot, index) => {
    if (index === currentSlide) {
      dot.classList.add('active');
    } else {
      dot.classList.remove('active');
    }
  });
}

// Go to previous slide
function goToPrevSlide() {
  currentSlide = (currentSlide === 0) ? testimonialSlides.length - 1 : currentSlide - 1;
  updateSlidePosition();
  resetAutoplay();
}

// Go to next slide
function goToNextSlide() {
  currentSlide = (currentSlide === testimonialSlides.length - 1) ? 0 : currentSlide + 1;
  updateSlidePosition();
  resetAutoplay();
}

// Go to specific slide
function goToSlide(index) {
  currentSlide = index;
  updateSlidePosition();
  resetAutoplay();
}

// Reset autoplay timer
function resetAutoplay() {
  if (autoplayInterval) {
    clearInterval(autoplayInterval);
  }
  autoplayInterval = setInterval(goToNextSlide, autoplayDelay);
}

// Calculate slide width
function calculateSlideWidth() {
  if (testimonialSlides.length > 0) {
    // Get the width of the slider container instead of the slide
    // This ensures we're moving exactly one container width
    slideWidth = document.querySelector('.testimonials-slider').offsetWidth;
    updateSlidePosition();
  }
}

// Initialize testimonials slider
function initTestimonials() {
  if (!testimonialTrack || testimonialSlides.length === 0) return;

  // Calculate initial slide width
  calculateSlideWidth();

  // Add event listeners for controls
  if (prevButton) prevButton.addEventListener('click', goToPrevSlide);
  if (nextButton) nextButton.addEventListener('click', goToNextSlide);

  // Add event listeners for dots
  dots.forEach((dot, index) => {
    dot.addEventListener('click', () => goToSlide(index));
  });

  // Handle window resize with debounce for better performance
  let resizeTimer;
  window.addEventListener('resize', function() {
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(function() {
      calculateSlideWidth();
    }, 250);
  });

  // Start autoplay
  resetAutoplay();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initTestimonials);