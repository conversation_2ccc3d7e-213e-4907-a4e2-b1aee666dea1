// EmailJS Configuration
// Update these values with your actual EmailJS credentials
// See SETUP-EMAILJS.md for detailed setup instructions

window.EMAIL_CONFIG = {
  // Your EmailJS Service ID (from Gmail service setup)
  SERVICE_ID: 'service_36tnbnu',

  // Your EmailJS Template ID for contact form
  CONTACT_TEMPLATE_ID: 'template_6qt0bgh',

  // Your EmailJS Template ID for audit requests
  AUDIT_TEMPLATE_ID: 'template_fmk8vle',

  // Your EmailJS Public Key (from Account settings)
  PUBLIC_KEY: 'zI-I9TQexUHRKEAZm',

  // Target email address (where forms will be sent)
  TARGET_EMAIL: '<EMAIL>'
};

// Instructions:
// 1. Go to https://www.emailjs.com/ and create an account
// 2. Set up Gmail service and get SERVICE_ID
// 3. Create email templates and get TEMPLATE_IDs
// 4. Get your PUBLIC_KEY from account settings
// 5. Replace the placeholder values above with your actual values
// 6. See SETUP-EMAILJS.md for detailed step-by-step instructions
