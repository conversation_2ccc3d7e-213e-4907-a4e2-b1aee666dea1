// EmailJS Configuration and Service
// This handles sending emails through EmailJS for static hosting

class EmailService {
  constructor() {
    // EmailJS configuration from config file
    this.serviceId = window.EMAIL_CONFIG?.SERVICE_ID || 'service_36tnbnu';
    this.templateId = window.EMAIL_CONFIG?.CONTACT_TEMPLATE_ID || 'template_6qt0bgh';
    this.auditTemplateId = window.EMAIL_CONFIG?.AUDIT_TEMPLATE_ID || 'template_fmk8vle';
    this.publicKey = window.EMAIL_CONFIG?.PUBLIC_KEY || 'zI-I9TQexUHRKEAZm';
    this.targetEmail = window.EMAIL_CONFIG?.TARGET_EMAIL || '<EMAIL>';

    // Initialize EmailJS
    this.init();
  }

  init() {
    // Check if EmailJS is loaded
    if (typeof emailjs !== 'undefined') {
      console.log('EmailJS library loaded successfully');

      // Check if configuration is set up
      if (this.serviceId === 'YOUR_SERVICE_ID' || this.publicKey === 'YOUR_PUBLIC_KEY') {
        console.error('⚠️ EmailJS not configured! Please update js/config.js with your EmailJS credentials.');
        console.log('📖 See SETUP-EMAILJS.md for setup instructions');
        return;
      }

      emailjs.init(this.publicKey);
      console.log('✅ EmailJS initialized successfully');
    } else {
      console.error('❌ EmailJS library not loaded');
    }
  }

  // Validate email format
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Validate form data
  validateContactForm(data) {
    const errors = [];

    if (!data.name || data.name.trim().length < 2) {
      errors.push('Please enter your full name');
    }

    if (!data.email || !this.isValidEmail(data.email)) {
      errors.push('Please enter a valid email address');
    }

    if (!data['project-type']) {
      errors.push('Please select a project type');
    }

    if (!data.message || data.message.trim().length < 10) {
      errors.push('Please enter a message (at least 10 characters)');
    }

    // Validate current website URL if provided and project type requires it
    if (data['current-website'] && data['current-website'].trim()) {
      try {
        new URL(data['current-website']);
      } catch {
        errors.push('Please enter a valid website URL');
      }
    }

    return errors;
  }

  // Validate audit form data
  validateAuditForm(data) {
    const errors = [];

    if (!data.name || data.name.trim().length < 2) {
      errors.push('Please enter your full name');
    }

    if (!data.email || !this.isValidEmail(data.email)) {
      errors.push('Please enter a valid email address');
    }

    if (!data.website) {
      errors.push('Please enter your website URL');
    } else {
      try {
        new URL(data.website);
      } catch {
        errors.push('Please enter a valid website URL');
      }
    }

    return errors;
  }

  // Send contact form email
  async sendContactForm(formData) {
    try {
      console.log('📧 Attempting to send contact form...', formData);

      // Check if EmailJS is properly configured
      if (this.serviceId === 'YOUR_SERVICE_ID') {
        throw new Error('EmailJS not configured. Please update js/config.js with your EmailJS credentials.');
      }

      // Validate form data
      const errors = this.validateContactForm(formData);
      if (errors.length > 0) {
        console.error('❌ Validation errors:', errors);
        throw new Error(errors.join(', '));
      }

      // Prepare template parameters
      const templateParams = {
        to_email: this.targetEmail,
        from_name: formData.name,
        from_email: formData.email,
        phone: formData.phone || 'Not provided',
        project_type: formData['project-type'],
        current_website: formData['current-website'] || 'Not provided',
        message: formData.message,
        selected_plan: formData['selected-plan'] || 'Not selected',
        selected_addons: formData['selected-addons'] || 'None',
        submission_date: new Date().toLocaleString()
      };

      console.log('📤 Sending email with params:', templateParams);

      // Send email via EmailJS
      const response = await emailjs.send(
        this.serviceId,
        this.templateId,
        templateParams
      );

      console.log('📬 EmailJS response:', response);

      if (response.status === 200) {
        console.log('✅ Email sent successfully!');
        return {
          success: true,
          message: 'Your message has been sent successfully! We\'ll get back to you soon.'
        };
      } else {
        throw new Error(`EmailJS returned status: ${response.status}`);
      }

    } catch (error) {
      console.error('❌ Contact form error:', error);

      // Provide more specific error messages
      let userMessage = 'Something went wrong. Please try again or contact us directly.';

      if (error.message.includes('not configured')) {
        userMessage = 'Email service not configured. Please contact the website administrator.';
      } else if (error.message.includes('Invalid template ID')) {
        userMessage = 'Email template not found. Please contact the website administrator.';
      } else if (error.message.includes('Invalid service ID')) {
        userMessage = 'Email service not found. Please contact the website administrator.';
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        userMessage = 'Network error. Please check your internet connection and try again.';
      }

      return {
        success: false,
        message: userMessage
      };
    }
  }

  // Send audit request email
  async sendAuditRequest(formData) {
    try {
      // Validate form data
      const errors = this.validateAuditForm(formData);
      if (errors.length > 0) {
        throw new Error(errors.join(', '));
      }

      // Prepare template parameters
      const templateParams = {
        to_email: this.targetEmail,
        from_name: formData.name,
        from_email: formData.email,
        website: formData.website,
        phone: formData.phone || 'Not provided',
        submission_date: new Date().toLocaleString()
      };

      // Send email via EmailJS
      const response = await emailjs.send(
        this.serviceId,
        this.auditTemplateId,
        templateParams
      );

      if (response.status === 200) {
        return {
          success: true,
          message: 'Thank you! We\'ll send your free audit within 24 hours.'
        };
      } else {
        throw new Error('Failed to send email');
      }

    } catch (error) {
      console.error('Audit form error:', error);
      return {
        success: false,
        message: error.message || 'Something went wrong. Please try again or contact us directly.'
      };
    }
  }
}

// Create global instance
window.emailService = new EmailService();

// Initialize when EmailJS library is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Check if EmailJS script is loaded, if not, load it
  if (typeof emailjs === 'undefined') {
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js';
    script.onload = () => {
      window.emailService.init();
    };
    document.head.appendChild(script);
  }
});
