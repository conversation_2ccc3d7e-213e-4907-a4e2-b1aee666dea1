// Conditional Form Fields Handler
class ConditionalFormFields {
  constructor() {
    this.projectTypeSelect = null;
    this.currentWebsiteGroup = null;
    this.currentWebsiteInput = null;
    this.init();
  }

  init() {
    this.projectTypeSelect = document.getElementById('project-type');
    this.currentWebsiteGroup = document.getElementById('current-website-group');
    this.currentWebsiteInput = document.getElementById('current-website');

    if (!this.projectTypeSelect || !this.currentWebsiteGroup || !this.currentWebsiteInput) {
      console.warn('Conditional form elements not found');
      return;
    }

    this.bindEvents();
  }

  bindEvents() {
    // Listen for changes in project type selection
    this.projectTypeSelect.addEventListener('change', (e) => {
      this.handleProjectTypeChange(e.target.value);
    });

    // Also check on page load in case there's a pre-selected value
    if (this.projectTypeSelect.value) {
      this.handleProjectTypeChange(this.projectTypeSelect.value);
    }
  }

  handleProjectTypeChange(selectedValue) {
    // Show website URL field for all project types except "new-website" and "other"
    const shouldShowWebsiteField = selectedValue && 
                                  selectedValue !== 'new-website' && 
                                  selectedValue !== 'other' &&
                                  selectedValue !== '';

    if (shouldShowWebsiteField) {
      this.showWebsiteField();
    } else {
      this.hideWebsiteField();
    }
  }

  showWebsiteField() {
    // Show the field with smooth animation
    this.currentWebsiteGroup.style.display = 'block';
    
    // Add required attribute when field is visible
    this.currentWebsiteInput.setAttribute('required', 'required');
    
    // Smooth fade-in animation
    setTimeout(() => {
      this.currentWebsiteGroup.style.opacity = '0';
      this.currentWebsiteGroup.style.transform = 'translateY(-10px)';
      this.currentWebsiteGroup.style.transition = 'all 0.3s ease';
      
      // Trigger animation
      requestAnimationFrame(() => {
        this.currentWebsiteGroup.style.opacity = '1';
        this.currentWebsiteGroup.style.transform = 'translateY(0)';
      });
    }, 10);

    // Update label text based on project type
    this.updateFieldLabel();
  }

  hideWebsiteField() {
    // Remove required attribute when field is hidden
    this.currentWebsiteInput.removeAttribute('required');
    
    // Clear the field value
    this.currentWebsiteInput.value = '';
    
    // Smooth fade-out animation
    this.currentWebsiteGroup.style.transition = 'all 0.3s ease';
    this.currentWebsiteGroup.style.opacity = '0';
    this.currentWebsiteGroup.style.transform = 'translateY(-10px)';
    
    // Hide after animation completes
    setTimeout(() => {
      this.currentWebsiteGroup.style.display = 'none';
    }, 300);
  }

  updateFieldLabel() {
    const label = this.currentWebsiteGroup.querySelector('label');
    const selectedValue = this.projectTypeSelect.value;
    
    // Customize label text based on project type
    switch (selectedValue) {
      case 'redesign':
        label.textContent = 'Current Website URL (to be redesigned)';
        this.currentWebsiteInput.placeholder = 'https://yourcurrentwebsite.com';
        break;
      case 'seo':
        label.textContent = 'Website URL (for SEO analysis)';
        this.currentWebsiteInput.placeholder = 'https://yourwebsite.com';
        break;
      case 'maintenance':
        label.textContent = 'Website URL (requiring maintenance)';
        this.currentWebsiteInput.placeholder = 'https://yourwebsite.com';
        break;
      default:
        label.textContent = 'Current Website URL';
        this.currentWebsiteInput.placeholder = 'https://yourwebsite.com';
        break;
    }
  }

  // Method to get form data including conditional fields
  getFormData() {
    const formData = new FormData(document.getElementById('contact-form'));
    const data = Object.fromEntries(formData.entries());
    
    // Only include website URL if the field is visible and has a value
    if (this.currentWebsiteGroup.style.display !== 'none' && this.currentWebsiteInput.value) {
      data['current-website'] = this.currentWebsiteInput.value;
    }
    
    return data;
  }

  // Method to validate conditional fields
  validateConditionalFields() {
    // If website field is visible and required, validate it
    if (this.currentWebsiteGroup.style.display !== 'none' && 
        this.currentWebsiteInput.hasAttribute('required')) {
      
      const value = this.currentWebsiteInput.value.trim();
      
      if (!value) {
        this.showFieldError('Please enter your current website URL');
        return false;
      }
      
      // Basic URL validation
      try {
        new URL(value);
      } catch (e) {
        this.showFieldError('Please enter a valid website URL (e.g., https://yourwebsite.com)');
        return false;
      }
    }
    
    this.clearFieldError();
    return true;
  }

  showFieldError(message) {
    // Remove existing error
    this.clearFieldError();
    
    // Create error element
    const errorElement = document.createElement('div');
    errorElement.className = 'field-error';
    errorElement.textContent = message;
    errorElement.style.color = 'var(--color-error, #ef4444)';
    errorElement.style.fontSize = '1.3rem';
    errorElement.style.marginTop = 'var(--space-1, 0.5rem)';
    
    // Add error after the input
    this.currentWebsiteInput.parentNode.appendChild(errorElement);
    
    // Add error styling to input
    this.currentWebsiteInput.style.borderColor = 'var(--color-error, #ef4444)';
  }

  clearFieldError() {
    // Remove error message
    const existingError = this.currentWebsiteGroup.querySelector('.field-error');
    if (existingError) {
      existingError.remove();
    }
    
    // Reset input styling
    this.currentWebsiteInput.style.borderColor = '';
  }

  // Method to reset the form
  reset() {
    this.hideWebsiteField();
    this.clearFieldError();
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const conditionalFields = new ConditionalFormFields();
  
  // Make it globally accessible for form validation
  window.conditionalFields = conditionalFields;
  
  // Integrate with existing form submission if present
  const contactForm = document.getElementById('contact-form');
  if (contactForm) {
    contactForm.addEventListener('submit', (e) => {
      // Validate conditional fields before submission
      if (!conditionalFields.validateConditionalFields()) {
        e.preventDefault();
        return false;
      }
    });
  }
});
