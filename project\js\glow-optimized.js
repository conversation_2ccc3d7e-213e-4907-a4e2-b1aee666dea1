// Optimized Glow Effects with Random Movement
(function() {
  'use strict';

  // Performance checks
  const isMobile = window.matchMedia('(max-width: 768px)').matches;
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  const isLowEnd = navigator.hardwareConcurrency <= 2 || navigator.deviceMemory <= 2;

  // Skip glow effects entirely for low-end devices or reduced motion
  if (prefersReducedMotion || isLowEnd) {
    return;
  }

  // Create glow container immediately to prevent layout shifts
  const glowContainer = document.createElement('div');
  glowContainer.className = 'glow-container';
  glowContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    overflow: hidden;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.5s ease;
  `;

  // Insert immediately to reserve space
  document.body.insertBefore(glowContainer, document.body.firstChild);

  // Glow configuration with random movement
  const glowConfig = isMobile ? [
    // Mobile: Only 2 static glows for performance
    { x: 25, y: 20, size: 400, color: 'rgba(147, 51, 234, 0.4)', blur: 60 },
    { x: 75, y: 80, size: 400, color: 'rgba(59, 130, 246, 0.4)', blur: 60 }
  ] : [
    // Desktop: 4 glows with random movement
    { x: 20, y: 15, size: 500, color: 'rgba(147, 51, 234, 0.5)', blur: 70, move: true },
    { x: 80, y: 20, size: 550, color: 'rgba(59, 130, 246, 0.5)', blur: 80, move: true },
    { x: 75, y: 85, size: 500, color: 'rgba(168, 85, 247, 0.4)', blur: 70, move: true },
    { x: 25, y: 80, size: 500, color: 'rgba(37, 99, 235, 0.4)', blur: 70, move: true }
  ];

  // Create glow elements with constant synchronized movement
  function createGlows() {
    const fragment = document.createDocumentFragment();

    // Add CSS animations for constant movement
    addMovementCSS();

    glowConfig.forEach((glow, index) => {
      const element = document.createElement('div');
      element.style.cssText = `
        position: absolute;
        left: ${glow.x}%;
        top: ${glow.y}%;
        width: ${glow.size}px;
        height: ${glow.size}px;
        background: radial-gradient(circle, ${glow.color} 0%, transparent 80%);
        border-radius: 50%;
        filter: blur(${glow.blur}px);
        transform: translate(-50%, -50%);
        will-change: transform;
      `;

      // Add constant movement animation for desktop
      if (glow.move && !isMobile) {
        element.style.animation = `glowMove${index + 1} ${15 + index * 2}s ease-in-out infinite`;
      }

      fragment.appendChild(element);
    });

    glowContainer.appendChild(fragment);
  }

  // Add CSS animations for random individual movement
  function addMovementCSS() {
    if (isMobile) return;

    const style = document.createElement('style');
    style.textContent = `
      @keyframes glowMove1 {
        0% { left: 20%; top: 15%; }
        12% { left: 45%; top: 8%; }
        27% { left: 32%; top: 42%; }
        41% { left: 8%; top: 28%; }
        58% { left: 38%; top: 12%; }
        73% { left: 15%; top: 35%; }
        89% { left: 28%; top: 22%; }
        100% { left: 20%; top: 15%; }
      }

      @keyframes glowMove2 {
        0% { left: 80%; top: 20%; }
        15% { left: 92%; top: 45%; }
        33% { left: 65%; top: 38%; }
        48% { left: 88%; top: 12%; }
        64% { left: 72%; top: 33%; }
        79% { left: 95%; top: 25%; }
        91% { left: 78%; top: 8%; }
        100% { left: 80%; top: 20%; }
      }

      @keyframes glowMove3 {
        0% { left: 75%; top: 85%; }
        18% { left: 55%; top: 92%; }
        29% { left: 88%; top: 68%; }
        45% { left: 62%; top: 78%; }
        61% { left: 82%; top: 95%; }
        76% { left: 68%; top: 72%; }
        88% { left: 90%; top: 88%; }
        100% { left: 75%; top: 85%; }
      }

      @keyframes glowMove4 {
        0% { left: 25%; top: 80%; }
        21% { left: 8%; top: 92%; }
        36% { left: 42%; top: 65%; }
        52% { left: 12%; top: 88%; }
        67% { left: 35%; top: 95%; }
        81% { left: 18%; top: 72%; }
        94% { left: 38%; top: 85%; }
        100% { left: 25%; top: 80%; }
      }
    `;
    document.head.appendChild(style);
  }

  // Initialize glow effects
  function init() {
    const initGlows = () => {
      createGlows();

      // Fade in the glow container
      requestAnimationFrame(() => {
        glowContainer.style.opacity = '1';
      });
    };

    if (window.requestIdleCallback) {
      requestIdleCallback(initGlows, { timeout: 1000 });
    } else {
      setTimeout(initGlows, 100);
    }
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();
