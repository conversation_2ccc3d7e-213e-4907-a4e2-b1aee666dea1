// Ultra-minimal JavaScript for maximum performance
(function() {
  'use strict';
  
  // Skip all animations on low-end devices or reduced motion
  const skipAnimations = window.matchMedia('(prefers-reduced-motion: reduce)').matches || 
                        navigator.hardwareConcurrency <= 2 || 
                        navigator.deviceMemory <= 2;
  
  // Cache critical DOM elements only
  let header, menuToggle, navMobile, scrollTopButton;
  
  // Minimal initialization
  function init() {
    // Remove no-js class
    document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
    
    // Cache only essential elements
    header = document.getElementById('header');
    menuToggle = document.querySelector('.menu-toggle');
    navMobile = document.querySelector('.nav-mobile');
    scrollTopButton = document.getElementById('scroll-top');
    
    // Make all content visible immediately
    makeContentVisible();
    
    // Initialize only essential functionality
    initNavbar();
    initScrollEffects();
    initTestimonials();
    initFAQ();
    initForm();
    
    // Simple animations only if not skipping
    if (!skipAnimations) {
      initSimpleAnimations();
    }
  }
  
  // Make all content visible immediately for performance
  function makeContentVisible() {
    const elements = document.querySelectorAll('.service-card, .process-step, section[data-animate]');
    elements.forEach(el => {
      el.style.cssText = 'opacity:1!important;visibility:visible!important;pointer-events:auto!important';
    });
  }
  
  // Minimal navbar functionality
  function initNavbar() {
    if (!menuToggle || !navMobile) return;
    
    menuToggle.onclick = () => {
      menuToggle.classList.toggle('active');
      navMobile.classList.toggle('active');
    };
    
    // Close on link click
    navMobile.onclick = (e) => {
      if (e.target.tagName === 'A') {
        menuToggle.classList.remove('active');
        navMobile.classList.remove('active');
      }
    };
  }
  
  // Minimal scroll effects
  function initScrollEffects() {
    let ticking = false;
    
    function updateScroll() {
      const scrollY = window.scrollY;
      
      if (header) {
        header.classList.toggle('scrolled', scrollY > 10);
      }
      
      if (scrollTopButton) {
        scrollTopButton.classList.toggle('visible', scrollY > 500);
        scrollTopButton.onclick = () => window.scrollTo({ top: 0, behavior: 'smooth' });
      }
      
      ticking = false;
    }
    
    window.addEventListener('scroll', () => {
      if (!ticking) {
        requestAnimationFrame(updateScroll);
        ticking = true;
      }
    }, { passive: true });
  }
  
  // Minimal testimonials
  function initTestimonials() {
    const track = document.querySelector('.testimonial-track');
    const slides = document.querySelectorAll('.testimonial-slide');
    const dots = document.querySelectorAll('.dot');
    
    if (!track || !slides.length) return;
    
    let current = 0;
    
    const update = () => {
      const width = track.parentElement.offsetWidth;
      track.style.transform = `translateX(-${current * width}px)`;
      dots.forEach((dot, i) => dot.classList.toggle('active', i === current));
    };
    
    const next = () => {
      current = (current + 1) % slides.length;
      update();
    };
    
    // Auto-advance
    setInterval(next, 5000);
    
    // Controls
    document.querySelectorAll('.control-next').forEach(btn => btn.onclick = next);
    document.querySelectorAll('.control-prev').forEach(btn => btn.onclick = () => {
      current = current === 0 ? slides.length - 1 : current - 1;
      update();
    });
    
    // Dots
    dots.forEach((dot, i) => dot.onclick = () => {
      current = i;
      update();
    });
    
    // Handle resize
    let resizeTimer;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(update, 100);
    });
    
    update();
  }
  
  // Minimal FAQ
  function initFAQ() {
    document.querySelectorAll('.faq-question').forEach(q => {
      q.onclick = () => q.parentElement.classList.toggle('active');
    });
  }
  
  // Minimal form handling
  function initForm() {
    const form = document.getElementById('contact-form');
    if (!form) return;
    
    form.onsubmit = (e) => {
      e.preventDefault();
      const response = form.querySelector('.form-response');
      if (response) {
        response.innerHTML = '<p style="color:var(--color-primary)">Thank you! We\'ll get back to you soon.</p>';
        setTimeout(() => {
          form.reset();
          response.innerHTML = '';
        }, 3000);
      }
    };
  }
  
  // Ultra-simple animations (only if performance allows)
  function initSimpleAnimations() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = '1';
          entry.target.style.transform = 'translateY(0)';
          observer.unobserve(entry.target);
        }
      });
    }, { rootMargin: '0px 0px -50px 0px' });
    
    // Only animate hero elements
    document.querySelectorAll('.hero h1, .hero p, .hero-cta').forEach((el, i) => {
      el.style.cssText = 'opacity:0;transform:translateY(20px);transition:all 0.6s ease';
      setTimeout(() => {
        el.style.opacity = '1';
        el.style.transform = 'translateY(0)';
      }, i * 100);
    });
  }
  
  // Initialize immediately if DOM is ready, otherwise wait
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();
