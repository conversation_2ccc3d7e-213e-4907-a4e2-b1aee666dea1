// Optimized JavaScript - Consolidated and Performance-Focused
(function() {
  'use strict';

  // Cache DOM elements to avoid repeated queries
  const DOM = {
    header: null,
    menuToggle: null,
    navMobile: null,
    scrollTopButton: null,
    testimonialTrack: null,
    testimonialSlides: null,
    testimonialControls: null,
    testimonialDots: null,
    faqItems: null,
    contactForm: null,
    sections: null,
    serviceCards: null,
    processSteps: null
  };

  // Performance optimizations
  const PERFORMANCE = {
    reducedMotion: false,
    isMobile: false,
    isLowEnd: false,
    observers: new Map(),
    timers: new Map()
  };

  // Initialize performance checks
  function initPerformanceChecks() {
    PERFORMANCE.reducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    PERFORMANCE.isMobile = window.matchMedia('(max-width: 768px)').matches;
    PERFORMANCE.isLowEnd = navigator.hardwareConcurrency <= 2 || navigator.deviceMemory <= 2;
  }

  // Optimized DOM caching
  function cacheDOMElements() {
    DOM.header = document.getElementById('header');
    DOM.menuToggle = document.querySelector('.menu-toggle');
    DOM.navMobile = document.querySelector('.nav-mobile');
    DOM.scrollTopButton = document.getElementById('scroll-top');
    DOM.testimonialTrack = document.querySelector('.testimonial-track');
    DOM.testimonialSlides = document.querySelectorAll('.testimonial-slide');
    DOM.testimonialControls = document.querySelectorAll('.control-prev, .control-next');
    DOM.testimonialDots = document.querySelectorAll('.dot');
    DOM.faqItems = document.querySelectorAll('.faq-item');
    DOM.contactForm = document.getElementById('contact-form');
    DOM.sections = document.querySelectorAll('section:not(.hero)');
    DOM.serviceCards = document.querySelectorAll('.service-card');
    DOM.processSteps = document.querySelectorAll('.process-step');

    // Immediate visibility for critical content
    DOM.serviceCards.forEach(card => {
      card.style.cssText = 'opacity:1;visibility:visible;pointer-events:auto';
    });

    DOM.processSteps.forEach(step => {
      step.style.cssText = 'opacity:1;visibility:visible;pointer-events:auto';
    });
  }

  // Debounced function utility
  function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  // Throttled function utility
  function throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    }
  }

  // Optimized scroll handler
  const handleScroll = throttle(() => {
    const scrollY = window.scrollY;

    // Header scroll effect
    if (DOM.header) {
      DOM.header.classList.toggle('scrolled', scrollY > 10);
    }

    // Scroll to top button
    if (DOM.scrollTopButton) {
      DOM.scrollTopButton.classList.toggle('visible', scrollY > 500);
    }
  }, 16); // ~60fps

  // Optimized navbar functionality
  function initNavbar() {
    if (!DOM.menuToggle || !DOM.navMobile) return;

    DOM.menuToggle.addEventListener('click', () => {
      DOM.menuToggle.classList.toggle('active');
      DOM.navMobile.classList.toggle('active');
    });

    // Close mobile menu when clicking on links
    DOM.navMobile.addEventListener('click', (e) => {
      if (e.target.tagName === 'A') {
        DOM.menuToggle.classList.remove('active');
        DOM.navMobile.classList.remove('active');
      }
    });
  }

  // Optimized testimonials slider
  function initTestimonials() {
    if (!DOM.testimonialTrack || DOM.testimonialSlides.length === 0) return;

    let currentSlide = 0;
    let autoplayTimer;

    const updateSlide = () => {
      const slideWidth = DOM.testimonialTrack.parentElement.offsetWidth;
      DOM.testimonialTrack.style.transform = `translateX(-${currentSlide * slideWidth}px)`;

      // Update dots
      DOM.testimonialDots.forEach((dot, index) => {
        dot.classList.toggle('active', index === currentSlide);
      });
    };

    const nextSlide = () => {
      currentSlide = (currentSlide + 1) % DOM.testimonialSlides.length;
      updateSlide();
    };

    const prevSlide = () => {
      currentSlide = currentSlide === 0 ? DOM.testimonialSlides.length - 1 : currentSlide - 1;
      updateSlide();
    };

    const goToSlide = (index) => {
      currentSlide = index;
      updateSlide();
    };

    const resetAutoplay = () => {
      if (autoplayTimer) clearInterval(autoplayTimer);
      autoplayTimer = setInterval(nextSlide, 5000);
    };

    // Event listeners
    DOM.testimonialControls.forEach(control => {
      control.addEventListener('click', () => {
        if (control.classList.contains('control-next')) {
          nextSlide();
        } else {
          prevSlide();
        }
        resetAutoplay();
      });
    });

    DOM.testimonialDots.forEach((dot, index) => {
      dot.addEventListener('click', () => {
        goToSlide(index);
        resetAutoplay();
      });
    });

    // Handle resize
    window.addEventListener('resize', debounce(updateSlide, 250));

    // Initialize
    updateSlide();
    resetAutoplay();
  }

  // Optimized FAQ functionality
  function initFAQ() {
    if (!DOM.faqItems.length) return;

    DOM.faqItems.forEach(item => {
      const question = item.querySelector('.faq-question');
      if (question) {
        question.addEventListener('click', () => {
          item.classList.toggle('active');
        });
      }
    });
  }

  // Optimized form handling
  function initForm() {
    if (!DOM.contactForm) return;

    DOM.contactForm.addEventListener('submit', (e) => {
      e.preventDefault();

      const formData = new FormData(DOM.contactForm);
      const responseDiv = DOM.contactForm.querySelector('.form-response');

      if (responseDiv) {
        responseDiv.innerHTML = '<p style="color: var(--color-primary);">Thank you for your message! We\'ll get back to you soon.</p>';
      }

      // Reset form after a delay
      setTimeout(() => {
        DOM.contactForm.reset();
        if (responseDiv) responseDiv.innerHTML = '';
      }, 3000);
    });
  }

  // Optimized scroll to top
  function initScrollToTop() {
    if (!DOM.scrollTopButton) return;

    DOM.scrollTopButton.addEventListener('click', () => {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    });
  }

  // Simplified animations for performance
  function initAnimations() {
    if (PERFORMANCE.reducedMotion || PERFORMANCE.isLowEnd) {
      // Skip animations for reduced motion or low-end devices
      DOM.sections.forEach(section => {
        section.style.opacity = '1';
        section.style.visibility = 'visible';
        section.style.pointerEvents = 'auto';
      });
      DOM.serviceCards.forEach(card => {
        card.style.opacity = '1';
        card.style.visibility = 'visible';
        card.style.pointerEvents = 'auto';
      });
      DOM.processSteps.forEach(step => {
        step.style.opacity = '1';
        step.style.visibility = 'visible';
        step.style.pointerEvents = 'auto';
      });
      return;
    }

    // Use a single intersection observer for all animations
    const animationObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const section = entry.target;
          section.classList.add('fade-in');

          // Special handling for services section - Mexican wave animation
          if (section.classList.contains('services')) {
            animateServiceCards(section);
          }

          // Special handling for process section - horizontal wave animation
          if (section.classList.contains('process')) {
            animateProcessSteps(section);
          }

          animationObserver.unobserve(entry.target);
        }
      });
    }, {
      rootMargin: '0px 0px -10% 0px',
      threshold: 0.1
    });

    // Observe sections
    DOM.sections.forEach(section => {
      section.setAttribute('data-animate', 'true');
      animationObserver.observe(section);
    });

    // Animate hero elements immediately
    const heroElements = document.querySelectorAll('.hero h1, .hero p, .hero-cta');
    heroElements.forEach((element, index) => {
      setTimeout(() => element.classList.add('fade-in'), index * 100);
    });
  }

  // Animate service cards with Mexican wave effect
  function animateServiceCards(servicesSection) {
    const serviceCards = servicesSection.querySelectorAll('.service-card');

    // Make sure all cards are initially hidden but in correct position
    serviceCards.forEach(card => {
      card.style.opacity = '0';
      card.style.transform = 'translateY(0)';
      card.style.visibility = 'hidden';
    });

    // Add wave-animate class to trigger Mexican wave animation
    setTimeout(() => {
      serviceCards.forEach(card => {
        card.classList.add('wave-animate');
      });
    }, 200);
  }

  // Animate process steps with horizontal wave effect
  function animateProcessSteps(processSection) {
    const processSteps = processSection.querySelectorAll('.process-step');

    // Make sure all steps are initially hidden but in correct position
    processSteps.forEach(step => {
      step.style.opacity = '0';
      step.style.transform = 'translateX(0)';
      step.style.visibility = 'hidden';
    });

    // Add wave-animate class to trigger horizontal wave animation
    setTimeout(() => {
      processSteps.forEach(step => {
        step.classList.add('wave-animate');
      });
    }, 200);
  }

  // Main initialization function
  function init() {
    // Remove no-js class to indicate JavaScript is working
    document.documentElement.classList.remove('no-js');
    document.documentElement.classList.add('js');

    initPerformanceChecks();
    cacheDOMElements();

    // Initialize components
    initNavbar();
    initTestimonials();
    initFAQ();
    initForm();
    initScrollToTop();
    initAnimations();

    // Add scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true });

    // Fallback: ensure content is visible after 3 seconds if animations fail
    setTimeout(() => {
      ensureContentVisible();
    }, 3000);

    // Clean up on page unload
    window.addEventListener('beforeunload', () => {
      PERFORMANCE.observers.forEach(observer => observer.disconnect());
      PERFORMANCE.timers.forEach(timer => clearTimeout(timer));
    });
  }

  // Fallback function to ensure content is visible
  function ensureContentVisible() {
    // Make sure all sections are visible
    DOM.sections.forEach(section => {
      if (section.style.opacity === '0' || section.style.opacity === '') {
        section.style.opacity = '1';
        section.style.visibility = 'visible';
        section.style.pointerEvents = 'auto';
      }
    });

    // Make sure all service cards are visible
    DOM.serviceCards.forEach(card => {
      if (card.style.opacity === '0' || card.style.opacity === '') {
        card.style.opacity = '1';
        card.style.visibility = 'visible';
        card.style.pointerEvents = 'auto';
      }
    });

    // Make sure all process steps are visible
    DOM.processSteps.forEach(step => {
      if (step.style.opacity === '0' || step.style.opacity === '') {
        step.style.opacity = '1';
        step.style.visibility = 'visible';
        step.style.pointerEvents = 'auto';
      }
    });
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }
})();
