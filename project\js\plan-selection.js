// Plan Selection and Management System
class PlanSelector {
  constructor() {
    this.selectedPlan = null;
    this.selectedAddons = [];
    this.init();
  }

  init() {
    this.bindPlanButtons();
    this.bindAddonButtons();
    this.loadSavedSelection();
    this.updateContactSection();
  }

  bindPlanButtons() {
    // Get all plan selection buttons
    const planButtons = document.querySelectorAll('.pricing-card .btn');

    planButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();

        const card = button.closest('.pricing-card');
        const planName = this.extractPlanName(card);
        const planPrice = this.extractPlanPrice(card);

        // Don't treat add-ons card as a plan selection
        if (card.classList.contains('addons-card')) {
          this.scrollToContact();
          return;
        }

        this.selectPlan({
          name: planName,
          price: planPrice,
          element: card
        });

        this.scrollToContact();
      });
    });
  }

  bindAddonButtons() {
    console.log('🔧 Setting up custom checkboxes for add-ons...');

    // Create individual addon selection buttons
    const addonItems = document.querySelectorAll('.addons-card .pricing-features li');
    console.log(`Found ${addonItems.length} addon items`);

    addonItems.forEach((item, index) => {
      const addonName = this.extractAddonName(item);
      const addonPrice = this.extractAddonPrice(item);

      console.log(`Processing addon ${index + 1}: ${addonName} - ${addonPrice}`);

      // Remove the existing SVG icon
      const existingSvg = item.querySelector('svg');
      if (existingSvg) {
        existingSvg.remove();
        console.log(`Removed SVG for ${addonName}`);
      }

      // Create a checkbox for each addon
      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.className = 'addon-checkbox';
      checkbox.dataset.addonName = addonName;
      checkbox.dataset.addonPrice = addonPrice;
      checkbox.id = `addon-${addonName.replace(/\s+/g, '-').toLowerCase()}`;

      // Create custom checkbox visual element
      const customCheckbox = document.createElement('span');
      customCheckbox.className = 'checkbox-custom';

      // Create a container for the checkbox elements
      const checkboxContainer = document.createElement('div');
      checkboxContainer.style.position = 'relative';
      checkboxContainer.style.display = 'inline-block';
      checkboxContainer.style.marginRight = '12px';

      // Add checkbox and custom element to container
      checkboxContainer.appendChild(checkbox);
      checkboxContainer.appendChild(customCheckbox);

      // Insert checkbox container at the beginning of the item
      item.insertBefore(checkboxContainer, item.firstChild);

      console.log(`✅ Created custom checkbox for ${addonName}`);

      checkbox.addEventListener('change', (e) => {
        console.log(`Checkbox changed for ${addonName}: ${e.target.checked}`);

        // Add particle effect when checked
        if (e.target.checked) {
          console.log('🎆 Creating particle effect for:', addonName);
          this.createParticleEffect(customCheckbox);
        }

        this.toggleAddon({
          name: addonName,
          price: addonPrice,
          selected: e.target.checked
        });
      });
    });

    console.log('🎨 Custom checkboxes setup complete!');
  }

  extractPlanName(card) {
    const h3 = card.querySelector('h3');
    return h3 ? h3.textContent.trim() : 'Unknown Plan';
  }

  extractPlanPrice(card) {
    const priceElement = card.querySelector('.pricing-price');
    return priceElement ? priceElement.textContent.trim() : 'Price not available';
  }

  extractAddonName(item) {
    const span = item.querySelector('span');
    if (span) {
      // Extract just the addon name, removing the price part
      const text = span.textContent.trim();
      return text.split(' +£')[0] || text.split(' <strong>')[0] || text;
    }
    return 'Unknown Addon';
  }

  extractAddonPrice(item) {
    const span = item.querySelector('span strong');
    return span ? span.textContent.trim() : 'Price not available';
  }

  selectPlan(plan) {
    // Remove previous selection styling
    document.querySelectorAll('.pricing-card').forEach(card => {
      card.classList.remove('selected-plan');
    });

    // Add selection styling to current plan
    plan.element.classList.add('selected-plan');

    // Save the selection
    this.selectedPlan = plan;
    this.saveSelection();
    this.updateContactSection();

    // Show success message
    this.showSelectionMessage(`${plan.name} selected!`);
  }

  toggleAddon(addon) {
    if (addon.selected) {
      // Add addon if not already selected
      if (!this.selectedAddons.find(a => a.name === addon.name)) {
        this.selectedAddons.push({
          name: addon.name,
          price: addon.price
        });
      }
    } else {
      // Remove addon
      this.selectedAddons = this.selectedAddons.filter(a => a.name !== addon.name);
    }

    this.saveSelection();
    this.updateContactSection();
    this.updateFormFields();
  }

  saveSelection() {
    const selection = {
      plan: this.selectedPlan,
      addons: this.selectedAddons,
      timestamp: new Date().toISOString()
    };

    sessionStorage.setItem('niteo-plan-selection', JSON.stringify(selection));
  }

  updateFormFields() {
    // Update hidden form fields with selection data
    const planField = document.getElementById('selected-plan');
    const addonsField = document.getElementById('selected-addons');

    if (planField) {
      planField.value = this.selectedPlan ? JSON.stringify(this.selectedPlan) : '';
    }

    if (addonsField) {
      addonsField.value = JSON.stringify(this.selectedAddons);
    }
  }

  loadSavedSelection() {
    const saved = sessionStorage.getItem('niteo-plan-selection');
    if (saved) {
      try {
        const selection = JSON.parse(saved);
        this.selectedPlan = selection.plan;
        this.selectedAddons = selection.addons || [];

        // Restore visual selection
        if (this.selectedPlan) {
          this.restoreVisualSelection();
        }
      } catch (e) {
        console.warn('Could not load saved plan selection:', e);
      }
    }
  }

  restoreVisualSelection() {
    // Find and mark the selected plan
    const planCards = document.querySelectorAll('.pricing-card:not(.addons-card)');
    planCards.forEach(card => {
      const planName = this.extractPlanName(card);
      if (planName === this.selectedPlan.name) {
        card.classList.add('selected-plan');
      }
    });

    // Restore addon selections
    this.selectedAddons.forEach(addon => {
      const checkbox = document.querySelector(`[data-addon-name="${addon.name}"]`);
      if (checkbox) {
        checkbox.checked = true;
      }
    });
  }

  updateContactSection() {
    let selectionHtml = '';

    if (this.selectedPlan) {
      selectionHtml += `
        <div class="form-group plan-selection-field">
          <label>Selected Package</label>
          <div class="selected-plan-display">
            <div class="plan-summary">
              <div class="plan-name">${this.selectedPlan.name}</div>
              <div class="plan-price">${this.selectedPlan.price}</div>
            </div>
      `;

      if (this.selectedAddons.length > 0) {
        selectionHtml += `
            <div class="selected-addons">
              <div class="addons-label">Selected Add-ons:</div>
              <div class="addons-list">
        `;

        this.selectedAddons.forEach(addon => {
          selectionHtml += `<span class="addon-item">${addon.name} - ${addon.price}</span>`;
        });

        selectionHtml += `
              </div>
            </div>
        `;
      }

      const totalPrice = this.calculateTotal();
      if (totalPrice) {
        selectionHtml += `
            <div class="total-price">
              <strong>Total: ${totalPrice}</strong>
            </div>
        `;
      }

      selectionHtml += `
            <button type="button" class="btn btn-secondary change-plan-btn">Change Plan</button>
          </div>
        </div>
      `;
    } else {
      selectionHtml = `
        <div class="form-group plan-selection-field">
          <label>Package Selection</label>
          <div class="no-plan-selected">
            <p>Please select a package above to get started</p>
            <a href="#pricing" class="btn btn-outline">View Packages</a>
          </div>
        </div>
      `;
    }

    // Insert or update the selection display in contact section
    this.insertSelectionDisplay(selectionHtml);
    this.updateFormFields();
  }

  insertSelectionDisplay(html) {
    const contactForm = document.querySelector('#contact-form');
    let selectionContainer = document.querySelector('.plan-selection-display');

    if (!selectionContainer) {
      selectionContainer = document.createElement('div');
      selectionContainer.className = 'plan-selection-display';

      // Insert after the project-type field but before the message field
      const messageGroup = contactForm.querySelector('div:has(#message)') ||
                          contactForm.querySelector('[for="message"]').parentElement;
      contactForm.insertBefore(selectionContainer, messageGroup);
    }

    selectionContainer.innerHTML = html;

    // Bind change plan button
    const changePlanBtn = selectionContainer.querySelector('.change-plan-btn');
    if (changePlanBtn) {
      changePlanBtn.addEventListener('click', () => {
        this.scrollToPricing();
      });
    }
  }

  calculateTotal() {
    // This is a simplified calculation - you might want to make this more sophisticated
    if (!this.selectedPlan) return null;

    // For now, just show that calculation is available
    // You could implement actual price parsing and calculation here
    return "Contact us for total pricing";
  }

  scrollToContact() {
    document.querySelector('#contact').scrollIntoView({
      behavior: 'smooth'
    });
  }

  scrollToPricing() {
    document.querySelector('#pricing').scrollIntoView({
      behavior: 'smooth'
    });
  }

  showSelectionMessage(message) {
    // Create a temporary notification
    const notification = document.createElement('div');
    notification.className = 'selection-notification';
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  createParticleEffect(checkboxElement) {
    console.log('🎆 Starting particle effect creation...');
    console.log('Checkbox element:', checkboxElement);

    // Create a particle container that's definitely outside any overflow constraints
    let particleContainer = document.getElementById('particle-container');
    if (!particleContainer) {
      particleContainer = document.createElement('div');
      particleContainer.id = 'particle-container';
      particleContainer.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        pointer-events: none !important;
        z-index: 99999 !important;
        overflow: visible !important;
      `;
      document.body.appendChild(particleContainer);
    }

    // Get the checkbox position relative to the viewport
    const checkboxRect = checkboxElement.getBoundingClientRect();

    console.log('Checkbox position:', checkboxRect);

    const particles = [];
    const particleCount = 8;

    // Create particles
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'checkbox-particle';

      // Style the particle - positioned absolutely within the full-screen container
      particle.style.cssText = `
        position: absolute !important;
        width: 10px !important;
        height: 10px !important;
        background: #9333ea !important;
        border-radius: 50% !important;
        pointer-events: none !important;
        z-index: 1 !important;
        top: ${checkboxRect.top + checkboxRect.height / 2}px !important;
        left: ${checkboxRect.left + checkboxRect.width / 2}px !important;
        transform: translate(-50%, -50%) !important;
        box-shadow: 0 0 6px #9333ea !important;
      `;

      // Append to the particle container that covers the full screen
      particleContainer.appendChild(particle);
      particles.push(particle);
      console.log(`Created particle ${i + 1}:`, particle);

      // Animate particle starting from edge of checkbox
      const angle = (360 / particleCount) * i;
      const checkboxRadius = 12; // Half the checkbox size (24px / 2)
      const explosionRadius = 120 + Math.random() * 80; // MASSIVE explosion radius
      const duration = 1500 + Math.random() * 800;

      const radians = (angle * Math.PI) / 180;

      // Start position: edge of the checkbox (relative to current position)
      const startX = Math.cos(radians) * checkboxRadius;
      const startY = Math.sin(radians) * checkboxRadius;

      // End position: much further out
      const endX = Math.cos(radians) * explosionRadius;
      const endY = Math.sin(radians) * explosionRadius;

      console.log(`Animating particle ${i + 1} from edge to:`, { startX, startY, endX, endY, duration });

      const animation = particle.animate([
        {
          transform: `translate(calc(-50% + ${startX}px), calc(-50% + ${startY}px)) scale(0.5)`,
          opacity: 1
        },
        {
          transform: `translate(calc(-50% + ${endX * 0.7}px), calc(-50% + ${endY * 0.7}px)) scale(1)`,
          opacity: 1,
          offset: 0.4
        },
        {
          transform: `translate(calc(-50% + ${endX}px), calc(-50% + ${endY}px)) scale(0)`,
          opacity: 0
        }
      ], {
        duration: duration,
        easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
      });

      animation.onfinish = () => {
        console.log(`Particle ${i + 1} animation finished, removing...`);
        if (particle.parentNode) {
          particle.remove();
        }
      };
    }

    // Add extra sparkle effect
    setTimeout(() => {
      console.log('✨ Creating sparkle effects...');
      for (let i = 0; i < 5; i++) {
        const sparkle = document.createElement('div');
        sparkle.className = 'checkbox-sparkle';

        sparkle.style.cssText = `
          position: absolute !important;
          width: 4px !important;
          height: 4px !important;
          background: white !important;
          border-radius: 50% !important;
          pointer-events: none !important;
          z-index: 10000 !important;
          top: ${30 + Math.random() * 40}% !important;
          left: ${30 + Math.random() * 40}% !important;
          box-shadow: 0 0 2px white !important;
        `;

        checkboxElement.appendChild(sparkle);
        console.log(`Created sparkle ${i + 1}:`, sparkle);

        const sparkleAnimation = sparkle.animate([
          { opacity: 0, transform: 'scale(0)' },
          { opacity: 1, transform: 'scale(1)' },
          { opacity: 0, transform: 'scale(0)' }
        ], {
          duration: 600,
          easing: 'ease-in-out'
        });

        sparkleAnimation.onfinish = () => {
          console.log(`Sparkle ${i + 1} finished, removing...`);
          if (sparkle.parentNode) {
            sparkle.remove();
          }
        };
      }
    }, 200);

    console.log('🎆 Particle effect setup complete!');
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PlanSelector();
});
