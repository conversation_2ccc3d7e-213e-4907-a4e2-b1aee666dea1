/**
 * Section Navigation Dots
 * Handles the side navigation dots that show current section and allow quick navigation
 */

class SectionNavigation {
  constructor() {
    this.sectionNav = document.getElementById('sectionNav');
    this.navDots = document.querySelectorAll('.nav-dot');
    this.sections = document.querySelectorAll('section[id], .hero');
    this.currentSection = 'hero';
    this.isScrolling = false;
    
    this.init();
  }

  init() {
    this.setupIntersectionObserver();
    this.bindEvents();
    this.showNavigation();
  }

  setupIntersectionObserver() {
    const options = {
      root: null,
      rootMargin: '-20% 0px -20% 0px', // Trigger when section is 20% visible
      threshold: 0.3
    };

    this.observer = new IntersectionObserver((entries) => {
      if (this.isScrolling) return;

      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const sectionId = entry.target.id || 'hero';
          this.updateActiveSection(sectionId);
        }
      });
    }, options);

    // Observe all sections
    this.sections.forEach(section => {
      this.observer.observe(section);
    });
  }

  bindEvents() {
    // Handle dot clicks
    this.navDots.forEach(dot => {
      dot.addEventListener('click', (e) => {
        e.preventDefault();
        const targetSection = dot.getAttribute('data-section');
        this.scrollToSection(targetSection);
      });
    });

    // Show/hide navigation based on scroll position
    let scrollTimeout;
    window.addEventListener('scroll', () => {
      this.showNavigation();
      
      // Hide after scrolling stops
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        if (window.scrollY < 100) {
          this.hideNavigation();
        }
      }, 2000);
    });

    // Show navigation on mouse movement near the right edge
    document.addEventListener('mousemove', (e) => {
      const windowWidth = window.innerWidth;
      if (e.clientX > windowWidth - 100) {
        this.showNavigation();
      }
    });
  }

  updateActiveSection(sectionId) {
    if (this.currentSection === sectionId) return;
    
    this.currentSection = sectionId;
    
    // Remove active class from all dots
    this.navDots.forEach(dot => {
      dot.classList.remove('active');
    });
    
    // Add active class to current section dot
    const activeDot = document.querySelector(`[data-section="${sectionId}"]`);
    if (activeDot) {
      activeDot.classList.add('active');
      
      // Add a subtle animation to the active dot
      const dotElement = activeDot.querySelector('.dot');
      if (dotElement) {
        dotElement.style.transform = 'scale(1.3)';
        setTimeout(() => {
          dotElement.style.transform = '';
        }, 200);
      }
    }
    
    console.log(`📍 Active section: ${sectionId}`);
  }

  scrollToSection(sectionId) {
    this.isScrolling = true;
    
    const targetElement = sectionId === 'hero' 
      ? document.querySelector('.hero') 
      : document.getElementById(sectionId);
    
    if (targetElement) {
      // Calculate offset for fixed header
      const headerHeight = 80;
      const targetPosition = targetElement.offsetTop - headerHeight;
      
      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      });
      
      // Update active state immediately for better UX
      this.updateActiveSection(sectionId);
      
      // Reset scrolling flag after animation
      setTimeout(() => {
        this.isScrolling = false;
      }, 1000);
    }
  }

  showNavigation() {
    if (window.scrollY > 100) {
      this.sectionNav.classList.add('visible');
    }
  }

  hideNavigation() {
    this.sectionNav.classList.remove('visible');
  }

  // Public method to manually set active section
  setActiveSection(sectionId) {
    this.updateActiveSection(sectionId);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  // Only initialize on desktop/tablet (navigation is hidden on mobile)
  if (window.innerWidth > 768) {
    window.sectionNavigation = new SectionNavigation();
  }
});

// Handle window resize
window.addEventListener('resize', () => {
  if (window.innerWidth <= 768 && window.sectionNavigation) {
    // Destroy on mobile
    window.sectionNavigation = null;
  } else if (window.innerWidth > 768 && !window.sectionNavigation) {
    // Initialize on desktop
    window.sectionNavigation = new SectionNavigation();
  }
});
