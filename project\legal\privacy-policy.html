<!DOCTYPE html>
<html lang="en" class="no-js">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <title>Privacy Policy | Niteo Studio | Web Design & SEO Agency</title>
  <meta name="description" content="Privacy Policy for Niteo Studio. Learn how we collect, use, and protect your personal information when you use our web design and SEO services.">
  <meta name="robots" content="index, follow">
  <meta name="author" content="Niteo Studio">

  <!-- Performance hints -->
  <meta http-equiv="Accept-CH" content="DPR, Viewport-Width, Width">
  <meta name="format-detection" content="telephone=no">

  <!-- Open Graph / Social Media -->
  <meta property="og:title" content="Privacy Policy | Niteo Studio">
  <meta property="og:description" content="Privacy Policy for Niteo Studio. Learn how we collect, use, and protect your personal information.">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://niteostudio.com/legal/privacy-policy">

  <!-- Favicon -->
  <link rel="icon" href="../assets/favicon.ico" type="image/x-icon">
  <link rel="apple-touch-icon" sizes="180x180" href="../assets/apple-touch-icon.png">

  <!-- Preconnect for performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Urbanist:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Font-display swap to prevent FOIT -->
  <style>
    /* Font fallbacks */
    @font-face {
      font-family: 'Bebas Neue Fallback';
      size-adjust: 100%;
      ascent-override: 90%;
      src: local('Arial');
      font-display: swap;
    }
    @font-face {
      font-family: 'Urbanist Fallback';
      size-adjust: 100%;
      ascent-override: 90%;
      src: local('Arial');
      font-display: swap;
    }

    /* Use fallbacks first */
    :root {
      --font-heading: 'Bebas Neue', 'Bebas Neue Fallback', sans-serif;
      --font-body: 'Urbanist', 'Urbanist Fallback', sans-serif;
    }
  </style>

  <!-- Critical CSS -->
  <style>
    :root {
      --color-bg: #080514;
      --color-bg-secondary: #0e0923;
      --color-primary: #9333ea;
      --color-primary-light: #a855f7;
      --color-text: #f8fafc;
      --color-text-muted: #94a3b8;
      --space-4: 1.6rem;
      --space-5: 2.4rem;
      --font-weight-medium: 500;
      --font-weight-bold: 700;
      --border-radius-md: 8px;
    }
    body {
      margin: 0;
      padding: 0;
      background: linear-gradient(135deg, #080514 0%, #0c0720 100%);
      color: #f8fafc;
      font-family: var(--font-body);
      font-size: 16px;
      line-height: 1.6;
    }
    .header {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 100;
      padding: var(--space-4) 0;
      background: rgba(8, 5, 20, 0.95);
      backdrop-filter: blur(10px);
    }
    .container {
      width: 90%;
      max-width: 1200px;
      margin: 0 auto;
    }
    .legal-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 120px 20px 60px;
    }
    .legal-header {
      text-align: center;
      margin-bottom: 60px;
    }
    .legal-header h1 {
      font-family: var(--font-heading);
      font-size: 4rem;
      letter-spacing: 2px;
      text-transform: uppercase;
      margin-bottom: 20px;
      color: var(--color-text);
    }
    .legal-header p {
      font-size: 1.2rem;
      color: var(--color-text-muted);
      margin-bottom: 0;
    }
    .back-link {
      display: inline-flex;
      align-items: center;
      color: var(--color-primary);
      text-decoration: none;
      font-weight: var(--font-weight-medium);
      margin-bottom: 40px;
      transition: color 0.3s ease;
    }
    .back-link:hover {
      color: var(--color-primary-light);
    }
    .back-link svg {
      margin-right: 8px;
    }
    .uc-privacy-policy {
      background: rgba(14, 9, 35, 0.5);
      border-radius: var(--border-radius-md);
      padding: 40px;
      border: 1px solid rgba(147, 51, 234, 0.2);
      color: var(--color-text);
      line-height: 1.7;
    }

    /* Style the privacy policy content */
    .uc-privacy-policy h1,
    .uc-privacy-policy h2,
    .uc-privacy-policy h3,
    .uc-privacy-policy h4,
    .uc-privacy-policy h5,
    .uc-privacy-policy h6 {
      color: var(--color-text) !important;
      font-family: var(--font-body) !important;
      font-weight: var(--font-weight-bold) !important;
      margin-top: 2rem !important;
      margin-bottom: 1rem !important;
    }

    .uc-privacy-policy h1 {
      font-size: 2.5rem !important;
      color: var(--color-primary) !important;
      border-bottom: 2px solid var(--color-primary) !important;
      padding-bottom: 0.5rem !important;
    }

    .uc-privacy-policy h2 {
      font-size: 2rem !important;
      color: var(--color-primary-light) !important;
    }

    .uc-privacy-policy h3 {
      font-size: 1.5rem !important;
    }

    .uc-privacy-policy p,
    .uc-privacy-policy li,
    .uc-privacy-policy span,
    .uc-privacy-policy div {
      color: var(--color-text) !important;
      font-family: var(--font-body) !important;
      font-size: 1rem !important;
      line-height: 1.7 !important;
      margin-bottom: 1rem !important;
    }

    .uc-privacy-policy ul,
    .uc-privacy-policy ol {
      margin-left: 1.5rem !important;
      margin-bottom: 1.5rem !important;
    }

    .uc-privacy-policy li {
      margin-bottom: 0.5rem !important;
      padding-left: 0.5rem !important;
    }

    .uc-privacy-policy a {
      color: var(--color-primary) !important;
      text-decoration: underline !important;
      transition: color 0.3s ease !important;
    }

    .uc-privacy-policy a:hover {
      color: var(--color-primary-light) !important;
    }

    .uc-privacy-policy strong,
    .uc-privacy-policy b {
      color: var(--color-text) !important;
      font-weight: var(--font-weight-bold) !important;
    }

    .uc-privacy-policy table {
      width: 100% !important;
      border-collapse: collapse !important;
      margin: 1.5rem 0 !important;
      background: rgba(8, 5, 20, 0.3) !important;
      border-radius: var(--border-radius-md) !important;
      overflow: hidden !important;
    }

    .uc-privacy-policy th,
    .uc-privacy-policy td {
      color: var(--color-text) !important;
      padding: 1rem !important;
      text-align: left !important;
      border-bottom: 1px solid rgba(147, 51, 234, 0.2) !important;
    }

    .uc-privacy-policy th {
      background: rgba(147, 51, 234, 0.2) !important;
      font-weight: var(--font-weight-bold) !important;
      color: var(--color-primary-light) !important;
    }

    .uc-privacy-policy blockquote {
      background: rgba(147, 51, 234, 0.1) !important;
      border-left: 4px solid var(--color-primary) !important;
      padding: 1rem 1.5rem !important;
      margin: 1.5rem 0 !important;
      border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0 !important;
      font-style: italic !important;
    }

    .uc-privacy-policy code {
      background: rgba(8, 5, 20, 0.5) !important;
      color: var(--color-primary-light) !important;
      padding: 0.2rem 0.4rem !important;
      border-radius: 4px !important;
      font-family: 'Courier New', monospace !important;
      font-size: 0.9rem !important;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .legal-container {
        padding: 100px 15px 40px;
      }

      .uc-privacy-policy {
        padding: 20px;
      }

      .uc-privacy-policy h1 {
        font-size: 2rem !important;
      }

      .uc-privacy-policy h2 {
        font-size: 1.5rem !important;
      }
    }
  </style>

  <!-- Main CSS -->
  <link rel="stylesheet" href="../css/all-styles.css">
  <link rel="stylesheet" href="../css/dark-theme.css">

  <!-- Privacy Policy Generator Script -->
  <script id="usercentrics-ppg" privacy-policy-id="************************************" src="https://policygenerator.usercentrics.eu/api/privacy-policy"></script>
</head>
<body class="dark-theme no-js">
  <script>document.body.classList.remove('no-js');</script>

  <header id="header" class="header">
    <div class="container">
      <div class="header-inner">
        <div class="logo">
          <a href="../">
            <div class="logo-icon"><img src="../assets/logo.png" alt="niteo studio logo" width="120" height="120"/></div>
          </a>
        </div>
        <nav class="nav-desktop">
          <ul>
            <li><a href="../#services">Services</a></li>
            <li><a href="../#portfolio">Work</a></li>
            <li><a href="../#process">Process</a></li>
            <li><a href="../#pricing">Pricing</a></li>
          </ul>
        </nav>
        <a href="../#contact" class="btn btn-primary desktop-only">Get a Free Quote</a>
        <button class="menu-toggle" aria-label="Toggle menu">
          <span class="bar"></span>
          <span class="bar"></span>
          <span class="bar"></span>
        </button>
      </div>
    </div>
    <nav class="nav-mobile">
      <ul>
        <li><a href="../#services">Services</a></li>
        <li><a href="../#portfolio">Work</a></li>
        <li><a href="../#process">Process</a></li>
        <li><a href="../#pricing">Pricing</a></li>
        <li><a href="../#contact" class="btn btn-primary">Get a Free Quote</a></li>
      </ul>
    </nav>
  </header>

  <main>
    <div class="legal-container">
      <a href="../" class="back-link">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <polyline points="15 18 9 12 15 6"></polyline>
        </svg>
        Back to Home
      </a>

      <div class="legal-header">
        <h1>Privacy Policy</h1>
        <p>How we collect, use, and protect your personal information</p>
      </div>

      <!-- Privacy Policy Content will be loaded here by the script -->
      <div class="uc-privacy-policy"></div>
    </div>
  </main>

  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-brand">
          <a href="../" class="footer-logo">
            <div class="logo-icon"><img src="../assets/logo.png" alt="Niteo Studio Logo" width="120" height="120" /></div>
          </a>
          <p class="footer-tagline">Custom websites that convert visitors into customers.</p>
        </div>
        <div class="footer-links">
          <div class="footer-group">
            <h4>Company</h4>
            <ul>
              <li><a href="../#services">Services</a></li>
              <li><a href="../#portfolio">Our Work</a></li>
              <li><a href="../#process">Process</a></li>
              <li><a href="../#pricing">Pricing</a></li>
            </ul>
          </div>
          <div class="footer-group">
            <h4>Resources</h4>
            <ul>
              <li><a href="#">Blog</a></li>
              <li><a href="#">Case Studies</a></li>
              <li><a href="#">Web Design Tips</a></li>
              <li><a href="#">SEO Guide</a></li>
            </ul>
          </div>
          <div class="footer-group">
            <h4>Legal</h4>
            <ul>
              <li><a href="privacy-policy.html">Privacy Policy</a></li>
              <li><a href="terms-of-service.html">Terms of Service</a></li>
              <li><a href="cookie-policy.html">Cookie Policy</a></li>
            </ul>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <div class="footer-social">
          <a href="#" aria-label="LinkedIn">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-linkedin">
              <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
              <rect x="2" y="9" width="4" height="12"></rect>
              <circle cx="4" cy="4" r="2"></circle>
            </svg>
          </a>
          <a href="#" aria-label="Twitter">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-twitter">
              <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
            </svg>
          </a>
          <a href="#" aria-label="Instagram">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-instagram">
              <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
              <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
              <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
            </svg>
          </a>
        </div>
        <div class="footer-copyright">
          <p>&copy; 2025 Niteo Studio. All rights reserved.</p>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript for mobile menu -->
  <script src="../js/minimal.js" defer></script>
</body>
</html>
