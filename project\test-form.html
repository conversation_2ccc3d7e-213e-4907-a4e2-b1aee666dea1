<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Form Test - Niteo Studio</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #9333ea;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #7c3aed;
        }
        .form-response {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
        }
        .form-response.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .form-response.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .debug-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 14px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            margin: 5px 0;
        }
        .status.ok { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Contact Form Test</h1>
        
        <div class="debug-info">
            <h3>Debug Information:</h3>
            <div id="debug-status">Loading...</div>
        </div>

        <form id="test-form">
            <div class="form-group">
                <label for="name">Name *</label>
                <input type="text" id="name" name="name" value="Test User" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email *</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="phone">Phone</label>
                <input type="tel" id="phone" name="phone" value="+1234567890">
            </div>
            
            <div class="form-group">
                <label for="project-type">Project Type *</label>
                <select id="project-type" name="project-type" required>
                    <option value="">Select project type</option>
                    <option value="new-website" selected>New Website</option>
                    <option value="redesign">Website Redesign</option>
                    <option value="seo">SEO Services</option>
                    <option value="maintenance">Maintenance</option>
                    <option value="other">Other</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="message">Message *</label>
                <textarea id="message" name="message" rows="4" required>This is a test message to verify the contact form is working correctly.</textarea>
            </div>
            
            <button type="submit">Send Test Message</button>
            
            <div class="form-response" id="form-response"></div>
        </form>
    </div>

    <!-- EmailJS -->
    <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
    
    <!-- Configuration and Email Service -->
    <script src="js/config.js"></script>
    <script src="js/email-service.js"></script>

    <script>
        // Debug status checker
        function updateDebugStatus() {
            const debugDiv = document.getElementById('debug-status');
            let status = '';
            
            // Check EmailJS library
            if (typeof emailjs !== 'undefined') {
                status += '<div class="status ok">✅ EmailJS library loaded</div>';
            } else {
                status += '<div class="status error">❌ EmailJS library not loaded</div>';
            }
            
            // Check configuration
            if (window.EMAIL_CONFIG) {
                status += '<div class="status ok">✅ Email config found</div>';
                
                if (window.EMAIL_CONFIG.SERVICE_ID !== 'YOUR_SERVICE_ID') {
                    status += '<div class="status ok">✅ Service ID configured</div>';
                } else {
                    status += '<div class="status warning">⚠️ Service ID not configured</div>';
                }
                
                if (window.EMAIL_CONFIG.PUBLIC_KEY !== 'YOUR_PUBLIC_KEY') {
                    status += '<div class="status ok">✅ Public key configured</div>';
                } else {
                    status += '<div class="status warning">⚠️ Public key not configured</div>';
                }
                
                if (window.EMAIL_CONFIG.CONTACT_TEMPLATE_ID !== 'YOUR_CONTACT_TEMPLATE_ID') {
                    status += '<div class="status ok">✅ Template ID configured</div>';
                } else {
                    status += '<div class="status warning">⚠️ Template ID not configured</div>';
                }
            } else {
                status += '<div class="status error">❌ Email config not found</div>';
            }
            
            // Check email service
            if (window.emailService) {
                status += '<div class="status ok">✅ Email service initialized</div>';
            } else {
                status += '<div class="status error">❌ Email service not initialized</div>';
            }
            
            debugDiv.innerHTML = status;
        }
        
        // Form submission handler
        document.getElementById('test-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const responseDiv = document.getElementById('form-response');
            const formData = new FormData(e.target);
            const formValues = Object.fromEntries(formData.entries());
            
            responseDiv.innerHTML = 'Sending test message...';
            responseDiv.className = 'form-response';
            
            try {
                if (!window.emailService) {
                    throw new Error('Email service not initialized');
                }
                
                const result = await window.emailService.sendContactForm(formValues);
                
                if (result.success) {
                    responseDiv.innerHTML = result.message;
                    responseDiv.className = 'form-response success';
                } else {
                    responseDiv.innerHTML = result.message;
                    responseDiv.className = 'form-response error';
                }
            } catch (error) {
                console.error('Test form error:', error);
                responseDiv.innerHTML = 'Error: ' + error.message;
                responseDiv.className = 'form-response error';
            }
        });
        
        // Update debug status when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(updateDebugStatus, 1000);
        });
    </script>
</body>
</html>
